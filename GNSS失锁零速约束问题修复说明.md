# GNSS失锁后零速约束失效问题修复说明

## 问题分析

### 根本原因
1. **零速约束应用条件过于严格**：原代码中零速约束只有在GNSS速度观测无效时才会应用
2. **零速检测阈值过于严格**：GNSS失锁后IMU零偏估计精度下降，难以满足严格的零速检测条件
3. **缺乏GNSS失锁后的自适应策略**：没有针对失锁状态的特殊处理

### 具体问题点
- `nav_kf.c:2700-2701` 零速约束应用条件：`measure_flag_ZUPT == SUCCESS && measure_flag_vn == NO`
- `nav_imu.c:608-617` 零速检测阈值过于严格
- 缺乏零速约束持续机制

## 修复方案

### 1. 调整零速检测阈值 (nav_const.h)
```c
// 新增GNSS失锁后的宽松阈值
#define TH_acc_GNSS_LOST    (15*0.542*0.001)  // 原来的3倍，进一步放宽
#define TH_gyr_GNSS_LOST    (15*0.013)        // 原来的3倍，进一步放宽
```

### 2. 改进零速检测逻辑 (nav_imu.c)
- GNSS失锁时自动使用宽松阈值
- 增加零速约束持续机制
- 在检测到零速后延长约束时间2秒
- **新增强制零速约束**：GNSS失锁且速度<0.5m/s时强制应用零速约束
- 支持系统标定状态(Nav_Status=3)和正常导航状态(Nav_Status=4)

### 3. 修改卡尔曼滤波零速约束应用条件 (nav_kf.c)
- 移除与GNSS速度观测的互斥条件
- 零速约束可以独立应用
- GNSS失锁时大幅增强零速约束权重（降低观测噪声到1/100）

### 4. 确保零速约束标志位正确设置 (nav_app.c)
- 根据ZUPT_flag正确设置measure_flag_ZUPT
- 避免被错误地设置为FAIL

### 5. 新增强制零速约束机制
- 当GNSS失锁且当前速度小于0.5m/s时，强制应用零速约束
- 这可以有效防止静止状态下的速度发散

## 修复效果预期

### 解决的问题
1. **GNSS失锁后速度发散**：零速约束能够有效约束速度误差
2. **北向速度异常增大**：通过大幅增强的零速约束权重(1/100)抑制速度发散
3. **静止后速度不收敛**：持续的零速约束和强制零速机制确保速度收敛到0附近
4. **晃动后速度累积**：改进的检测逻辑、持续机制和强制零速避免误差累积
5. **检测阈值过严**：放宽3倍的检测阈值提高零速检测成功率

### 性能改进
- 提高GNSS失锁后的导航精度
- 增强零速检测的鲁棒性
- 减少速度误差的累积效应
- **强制零速约束**确保低速状态下的稳定性
- 支持更多导航状态下的零速约束应用

## 测试建议

### 1. 静态测试
- 设备静止，人为断开GNSS信号
- 观察速度是否能保持在0附近
- 验证零速约束是否正常工作

### 2. 动态测试
- 设备运动后静止，GNSS失锁状态
- 观察速度是否能快速收敛到0
- 验证持续机制是否有效

### 3. 晃动测试
- GNSS失锁状态下轻微晃动设备
- 设备重新静止后观察速度收敛情况
- 验证改进的检测阈值是否合适

### 4. 长时间测试
- 长时间GNSS失锁状态下的性能
- 观察零偏估计和速度稳定性
- 验证系统整体稳定性

## 参数调优建议

### 零速检测阈值
- 如果仍然检测不到零速：进一步放宽阈值
- 如果误检测过多：适当收紧阈值
- 建议范围：TH_acc_GNSS_LOST = (10~20)*0.542*0.001

### 零速约束权重
- 当前设置为原来的1/100（增强100倍权重）
- 可根据实际效果调整：1/50到1/200之间
- 权重过大可能导致系统过于敏感，但对于失锁状态是必要的

### 强制零速约束速度阈值
- 当前设置为0.5m/s
- 可根据应用场景调整：0.2-1.0m/s
- 阈值过大可能影响低速运动检测

### 持续时间
- 当前设置为2秒
- 可根据应用场景调整：1-5秒
- 时间过长可能影响动态响应

## 注意事项

1. **编译测试**：修改后需要完整编译测试
2. **参数验证**：建议在实际环境中验证参数设置
3. **性能监控**：关注修改后的整体导航性能
4. **兼容性**：确保修改不影响GNSS正常工作时的性能

## 后续优化方向

1. **自适应阈值**：根据IMU性能动态调整检测阈值
2. **智能权重**：根据GNSS失锁时间动态调整零速约束权重
3. **多传感器融合**：结合其他传感器信息提高零速检测精度
4. **机器学习**：使用AI算法优化零速检测和约束策略
