# GNSS失锁零速约束问题修复说明 v2.0

## 问题现象
1. **GNSS失锁后速度发散**: 东向速度达到0.9m/s，说明惯性导航在积累误差
2. **位置轨迹呈环形**: 典型的陀螺漂移导致的圆形误差，设备在"画圈"
3. **零速约束失效**: 原有的零速约束条件在GNSS失锁后过于严格，无法有效抑制误差发散

## 根本原因分析
1. **阈值过严**: 原始的零速约束阈值在GNSS失锁后仍然使用正常情况下的严格标准
2. **缺乏分层策略**: 没有根据不同的运动状态和IMU数据质量采用不同的约束策略
3. **持续性不足**: 零速约束的持续时间和强度不足以抑制惯性导航的快速发散

## 修复策略

### 1. 多层次阈值策略
```c
// 正常情况下的严格阈值
#define TH_acc                      (0.542*0.001)
#define TH_gyr                      (0.013)

// GNSS失锁后的宽松阈值 (放宽20倍)
#define TH_acc_GNSS_LOST           (20*0.542*0.001)
#define TH_gyr_GNSS_LOST           (20*0.013)

// 速度阈值
#define SPEED_THRESHOLD_GNSS_LOST   1.0  // 1.0m/s以下考虑零速约束
```

### 2. 智能零速约束逻辑
```c
// GNSS失锁时的智能零速约束策略
if(gnss_lost) {
    // 策略1: 速度小于1.0m/s且IMU相对稳定 -> 应用零速约束
    if(speed < SPEED_THRESHOLD_GNSS_LOST && 
       acc_std < 3*th_acc_use && gyr_std < 3*th_gyr_use) {
        ZUPT_flag = SUCCESS;
        hold_cnt = ZUPT_HOLD_COUNT;
    }
    // 策略2: 速度小于0.5m/s且加速度很稳定 -> 强制零速约束
    else if(speed < 0.5 && acc_std < th_acc_use) {
        ZUPT_flag = SUCCESS;
        hold_cnt = ZUPT_HOLD_COUNT/2;
    }
    // 策略3: 持续期内保持约束
    else if(hold_cnt > 0) {
        ZUPT_flag = SUCCESS;
        hold_cnt--;
    }
    else {
        ZUPT_flag = FAIL;
    }
}
```

### 3. 关键改进点
1. **阈值自适应**: 根据GNSS状态动态调整阈值
2. **速度分层**: 不同速度范围采用不同的约束策略
3. **多重条件**: 结合速度、加速度、陀螺等多个指标
4. **持续控制**: 通过计数器控制约束的持续时间

## 预期效果
1. **抑制速度发散**: 在真正静止或低速时有效应用零速约束
2. **减少位置漂移**: 通过及时的零速约束减少积累误差
3. **避免误约束**: 在明显运动时不会错误地应用零速约束
4. **提高鲁棒性**: 系统在GNSS失锁后能够更稳定地工作

## 测试验证
建议在以下场景下测试修复效果：
1. **静止状态**: GNSS失锁后设备完全静止，应该能检测到零速约束
2. **低速运动**: 速度0.1-0.5m/s时，根据IMU稳定性决定是否约束
3. **中速运动**: 速度0.5-1.0m/s时，只在IMU很稳定时才约束
4. **高速运动**: 速度>1.0m/s时，不应该应用零速约束
5. **晃动测试**: 手持设备晃动时，不应该误触发零速约束

## 注意事项
1. 参数可能需要根据具体的IMU性能和应用场景进行微调
2. 建议在实际环境中进行充分测试
3. 如果效果仍不理想，可以进一步调整阈值或策略
