#include <stdio.h>
#include <math.h>

// 模拟修改后的分层零速约束逻辑
#define RETURN_SUCESS 1
#define RETURN_FAIL 0
#define ZUPT_HOLD_COUNT 200

// 模拟的阈值
#define TH_acc_NORMAL (5*0.542*0.001)
#define TH_gyr_NORMAL (5*0.013)
#define TH_acc_GNSS_LOST (30*0.542*0.001)
#define TH_gyr_GNSS_LOST (30*0.013)

// 分层速度阈值
#define SPEED_THRESHOLD_HIGH 2.0
#define SPEED_THRESHOLD_MID  1.0
#define SPEED_THRESHOLD_LOW  0.3

typedef struct {
    int ZUPT_flag;
    int ZUPT_hold_cnt;
    double vn[3];
    int gnss_lost;
    double acc_std;
    double gyr_std;
} SimData;

int simulate_layered_zupt(SimData* data) {
    double th_acc_use = TH_acc_NORMAL;
    double th_gyr_use = TH_gyr_NORMAL;
    
    double speed = sqrt(data->vn[0]*data->vn[0] + data->vn[1]*data->vn[1] + data->vn[2]*data->vn[2]);
    
    if(data->gnss_lost) {
        th_acc_use = TH_acc_GNSS_LOST;
        th_gyr_use = TH_gyr_GNSS_LOST;
        
        if(speed > SPEED_THRESHOLD_HIGH) {
            // 高速运动，绝不约束
            data->ZUPT_flag = RETURN_FAIL;
            data->ZUPT_hold_cnt = 0;
        }
        else if(speed < SPEED_THRESHOLD_LOW) {
            // 低速状态，宽松条件约束
            if(data->acc_std < 2*th_acc_use && data->gyr_std < 2*th_gyr_use) {
                data->ZUPT_flag = RETURN_SUCESS;
                data->ZUPT_hold_cnt = ZUPT_HOLD_COUNT;
            }
            else if(data->ZUPT_hold_cnt > 0) {
                data->ZUPT_flag = RETURN_SUCESS;
                data->ZUPT_hold_cnt--;
            }
            else {
                data->ZUPT_flag = RETURN_FAIL;
            }
        }
        else if(speed < SPEED_THRESHOLD_MID) {
            // 中速状态，严格条件约束
            if(data->acc_std < th_acc_use && data->gyr_std < th_gyr_use) {
                data->ZUPT_flag = RETURN_SUCESS;
                data->ZUPT_hold_cnt = ZUPT_HOLD_COUNT;
            }
            else if(data->ZUPT_hold_cnt > 0) {
                data->ZUPT_flag = RETURN_SUCESS;
                data->ZUPT_hold_cnt--;
            }
            else {
                data->ZUPT_flag = RETURN_FAIL;
            }
        }
        else {
            // 中高速状态(1.0-2.0m/s)，非常严格的条件
            if(data->acc_std < th_acc_use/2 && data->gyr_std < th_gyr_use/2) {
                data->ZUPT_flag = RETURN_SUCESS;
                data->ZUPT_hold_cnt = ZUPT_HOLD_COUNT/4;
            }
            else {
                data->ZUPT_flag = RETURN_FAIL;
                data->ZUPT_hold_cnt = 0;
            }
        }
    }
    else {
        // GNSS正常时使用原有逻辑
        if (data->acc_std < th_acc_use && data->gyr_std < th_gyr_use) {
            data->ZUPT_flag = RETURN_SUCESS;
        }
        else {
            data->ZUPT_flag = RETURN_FAIL;
            data->ZUPT_hold_cnt = 0;
        }
    }
    
    return data->ZUPT_flag;
}

int main() {
    SimData data = {0};
    
    printf("分层零速约束策略测试:\n");
    printf("阈值设置:\n");
    printf("  正常: acc=%.6f, gyr=%.6f\n", TH_acc_NORMAL, TH_gyr_NORMAL);
    printf("  失锁: acc=%.6f, gyr=%.6f\n", TH_acc_GNSS_LOST, TH_gyr_GNSS_LOST);
    printf("速度分层:\n");
    printf("  低速: <%.1fm/s (宽松条件)\n", SPEED_THRESHOLD_LOW);
    printf("  中速: %.1f-%.1fm/s (严格条件)\n", SPEED_THRESHOLD_LOW, SPEED_THRESHOLD_MID);
    printf("  中高速: %.1f-%.1fm/s (非常严格)\n", SPEED_THRESHOLD_MID, SPEED_THRESHOLD_HIGH);
    printf("  高速: >%.1fm/s (绝不约束)\n\n", SPEED_THRESHOLD_HIGH);
    
    // 测试各种速度下的表现
    double test_speeds[] = {0.1, 0.2, 0.5, 0.9, 1.2, 1.9, 2.5};
    int num_tests = sizeof(test_speeds) / sizeof(test_speeds[0]);
    
    printf("=== GNSS失锁场景测试 ===\n");
    for(int i = 0; i < num_tests; i++) {
        data.gnss_lost = 1;
        data.vn[0] = test_speeds[i] * 0.7;  // 东向
        data.vn[1] = test_speeds[i] * 0.7;  // 北向
        data.vn[2] = 0.0;
        data.acc_std = 0.008;  // 中等稳定性
        data.gyr_std = 0.02;
        data.ZUPT_flag = 0;
        data.ZUPT_hold_cnt = 0;
        
        int result = simulate_layered_zupt(&data);
        double actual_speed = sqrt(data.vn[0]*data.vn[0] + data.vn[1]*data.vn[1]);
        
        printf("速度: %.2fm/s -> ZUPT: %d, hold_cnt: %d", 
               actual_speed, result, data.ZUPT_hold_cnt);
        
        if(actual_speed < SPEED_THRESHOLD_LOW) {
            printf(" (低速-宽松)\n");
        } else if(actual_speed < SPEED_THRESHOLD_MID) {
            printf(" (中速-严格)\n");
        } else if(actual_speed < SPEED_THRESHOLD_HIGH) {
            printf(" (中高速-很严格)\n");
        } else {
            printf(" (高速-不约束)\n");
        }
    }
    
    // 特别测试您遇到的情况
    printf("\n=== 特殊场景测试 ===\n");
    
    // 场景1: 北向速度1.94m/s的情况
    printf("场景1: 北向速度1.94m/s (您遇到的情况)\n");
    data.gnss_lost = 1;
    data.vn[0] = -0.43;  // 东向
    data.vn[1] = 1.94;   // 北向
    data.vn[2] = -0.036;
    data.acc_std = 0.008;
    data.gyr_std = 0.02;
    data.ZUPT_flag = 0;
    data.ZUPT_hold_cnt = 0;
    
    int result = simulate_layered_zupt(&data);
    double speed = sqrt(data.vn[0]*data.vn[0] + data.vn[1]*data.vn[1]);
    printf("总速度: %.2fm/s -> ZUPT: %d, hold_cnt: %d\n", speed, result, data.ZUPT_hold_cnt);
    printf("分析: 速度%.2fm/s在中高速范围，需要非常严格的条件才能约束\n\n", speed);
    
    // 场景2: 晃动后静止的情况
    printf("场景2: 晃动后静止 (IMU数据很稳定)\n");
    data.gnss_lost = 1;
    data.vn[0] = 0.05;
    data.vn[1] = 0.08;
    data.vn[2] = 0.0;
    data.acc_std = 0.003;  // 很稳定
    data.gyr_std = 0.008;  // 很稳定
    data.ZUPT_flag = 0;
    data.ZUPT_hold_cnt = 0;
    
    result = simulate_layered_zupt(&data);
    speed = sqrt(data.vn[0]*data.vn[0] + data.vn[1]*data.vn[1]);
    printf("总速度: %.2fm/s -> ZUPT: %d, hold_cnt: %d\n", speed, result, data.ZUPT_hold_cnt);
    printf("分析: 低速且IMU很稳定，应该能够约束\n");
    
    return 0;
}
