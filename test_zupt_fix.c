#include <stdio.h>
#include <math.h>

// 模拟修改后的零速约束逻辑
#define RETURN_SUCESS 1
#define RETURN_FAIL 0
#define ZUPT_HOLD_COUNT 50

// 模拟的阈值
#define TH_acc_GNSS_LOST (10*0.542*0.001)
#define TH_gyr_GNSS_LOST (10*0.013)

typedef struct {
    int ZUPT_flag;
    int ZUPT_hold_cnt;
    double vn[3];  // 速度
} TestData;

// 模拟零速约束检测函数
int test_zupt_detection(double acc_std, double gyr_std, int gnss_lost, double speed, TestData* data) {
    double th_acc_use = TH_acc_GNSS_LOST;
    double th_gyr_use = TH_gyr_GNSS_LOST;
    
    if (acc_std < th_acc_use && gyr_std < th_gyr_use) {
        data->ZUPT_flag = RETURN_SUCESS;
        if(gnss_lost) {
            data->ZUPT_hold_cnt = ZUPT_HOLD_COUNT;
        }
    }
    // GNSS失锁且速度很小时，谨慎应用零速约束
    else if(gnss_lost && speed < 0.2 && acc_std < 2*th_acc_use && gyr_std < 2*th_gyr_use) {
        data->ZUPT_flag = RETURN_SUCESS;
        data->ZUPT_hold_cnt = ZUPT_HOLD_COUNT/2; // 减少持续时间
    }
    else {
        // 检查是否在持续期内
        if(gnss_lost && data->ZUPT_hold_cnt > 0) {
            data->ZUPT_flag = RETURN_SUCESS;  // 继续保持零速约束
            data->ZUPT_hold_cnt--;
        }
        else {
            data->ZUPT_flag = RETURN_FAIL;
            data->ZUPT_hold_cnt = 0;
        }
    }
    
    return data->ZUPT_flag;
}

int main() {
    TestData data = {0};
    
    printf("测试GNSS失锁后的零速约束逻辑:\n");
    printf("阈值: acc=%.6f, gyr=%.6f\n", TH_acc_GNSS_LOST, TH_gyr_GNSS_LOST);
    printf("\n");
    
    // 测试场景1: GNSS正常，满足零速约束条件
    printf("场景1: GNSS正常，满足零速约束\n");
    int result = test_zupt_detection(0.001, 0.005, 0, 0.1, &data);
    printf("结果: ZUPT=%d, hold_cnt=%d\n\n", result, data.ZUPT_hold_cnt);
    
    // 测试场景2: GNSS失锁，速度很小，IMU数据相对稳定
    printf("场景2: GNSS失锁，速度很小(0.1m/s)，IMU数据相对稳定\n");
    data.ZUPT_flag = 0; data.ZUPT_hold_cnt = 0;
    result = test_zupt_detection(0.008, 0.02, 1, 0.1, &data);
    printf("结果: ZUPT=%d, hold_cnt=%d\n\n", result, data.ZUPT_hold_cnt);
    
    // 测试场景3: GNSS失锁，速度较大，不应该强制零速约束
    printf("场景3: GNSS失锁，速度较大(0.5m/s)，不应该强制零速约束\n");
    data.ZUPT_flag = 0; data.ZUPT_hold_cnt = 0;
    result = test_zupt_detection(0.008, 0.02, 1, 0.5, &data);
    printf("结果: ZUPT=%d, hold_cnt=%d\n\n", result, data.ZUPT_hold_cnt);
    
    // 测试场景4: GNSS失锁，IMU数据不稳定，不应该零速约束
    printf("场景4: GNSS失锁，IMU数据不稳定，不应该零速约束\n");
    data.ZUPT_flag = 0; data.ZUPT_hold_cnt = 0;
    result = test_zupt_detection(0.02, 0.05, 1, 0.1, &data);
    printf("结果: ZUPT=%d, hold_cnt=%d\n\n", result, data.ZUPT_hold_cnt);
    
    return 0;
}
