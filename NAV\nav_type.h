#ifndef __NAV_TYPE_H_
#define __NAV_TYPE_H_
#include "nav_const.h"
//#include "NAV_MCU.h"
#include "algorithm.h"

////////////////////////////////////////////////////////
//������ؽṹ������
//��������ϵn�������� 
//��������ϵb����ǰ��
////////////////////////////////////////////////////////

//IMU////////////////////////////////////////////////////
typedef struct
{
	double acc_raw[3];
	double gyro_raw[3];
	double gyro_fog_raw[3];

	//float temp_mems_raw;
	//float temp_fog_raw;

    double temp_mems_raw;
	double temp_fog_raw;
	
	double acc[3];
	double gyro[3];
	double gyro_fog[3];
	
	double acc_pre[3];
	double gyro_pre[3];
	double gyro_fog_pre[3];
	
	//float temp_mems;
	//float temp_fog;

	double temp_mems;
	double temp_fog;
	
	double acc_use[3];//m/s2
	double gyro_use[3];//rad/s
	double acc_use_pre[3];
	double gyro_use_pre[3];
	double acc_use_kalman[3];
	double gyro_use_kalman[3];

	double acc_use_buffer[3*MAX_IMU_BUFF];//�洢ǰ20���Ӽ�����
	double gyro_use_buffer[3*MAX_IMU_BUFF];//�洢ǰ20����������
	
	//float temp_use;	
	double temp_use;	
}_IMU_t;
//GPS////////////////////////////////////////////////////
typedef struct
{
	double Lat;//��λ��
	double Lon;//��λ��
	double Altitude;//��λm
	double ve;//m/s
	double vn;
	double vu;
//	double Pitch;
// double Roll;//*********DEG***
 double Heading;//*********DEG***
	double Heading_cor;//****deg***
	//double delay_pps;     //gps��ʱ��ms
	unsigned char Position_Status;
	unsigned char rtkStatus;   //kinematic 0: �޶�λ 1�����㶨λ 2����ֽ� 3��  4��RTK�̶��� 5��RTK������
	unsigned char headingStatus;//movingbase 0: �޶�λ 1�����㶨λ 2����ֽ� 3��  4��RTK�̶��� 5��RTK������
	unsigned int 		gpssecond982_old;//982���ʱ��
}_GPS_UP_DAT_PRE;//*********gpsǰһ����������*****

typedef struct
{
//	double pos[3];
//	double vn[3];
//	double att[3];
	double Lat;//��λ��
	double Lon;//��λ��
	double Altitude;//��λm
	double ve;//m/s
	double vn;
	double vu;
 double Pitch;
 double Roll;
 double Heading;
	double Heading_cor;//****deg***
	double trackTrue;//������
	double baseline;//���߳���
	unsigned char Position_Status;
	unsigned char rtkStatus;   //kinematic 0: �޶�λ 1�����㶨λ 2����ֽ� 3��  4��RTK�̶��� 5��RTK������
	unsigned char headingStatus;//movingbase 0: �޶�λ 1�����㶨λ 2����ֽ� 3��  4��RTK�̶��� 5��RTK������
	unsigned char pre_rtkStatus; 
 unsigned char Sate_Num;	
 unsigned int	gpsweek;
 unsigned int gpssecond;//��������msΪ��λ,FPGA���ʱ��
	unsigned int        gpssecond_old;
 double delay_pps;     //gps��ʱ��ms
	double delay; 
	unsigned char  gps_up_flag;
	unsigned int 		gpssecond982;//982���ʱ��
	unsigned int   gpssecond982_old;
	//********
	_GPS_UP_DAT_PRE  GPS_UP_Pre;
	float pdop;
//	float hdop;
//	float vdop;
	unsigned char NO_RTK_heading_flag;//***�޲������ʱ��������Ч��־λ*****
}_GPS_t;

//Param//////////////////////////////////////////////////
typedef struct
{
	double gnssArmLength[3];
	double gyro_off[3];//rad/s
	double acc_off[3];//m/s2
	double gnssAtt_from_vehicle[3];//˫�������IMU�İ�װ�Ƕ�
	double gnssAtt_from_vehicle2[3];//˫�������IMU�İ�װ�Ƕ�,��λ��
	double OD_ArmLength[3];
	double OD_Att_from_vehicle[3];//���������IMU�İ�װ�Ƕ�
	unsigned char test_flag[7];
	double wb_set[3];
	//double scale_factor;
	//double scale_factor_filte;

}_Param_t;//



//earth//////////////////////////////////////////////////
typedef struct{
	//double Re0;
	double sL, cL, tL;//sinL cosL tanL,LΪά��ֵ
	double RMh, RNh,clRNh;
	double Wie;	
	//wnie�����崦������ת����Ľ��ٶ�
    //wnen:����������ϵ�½��ٶ�
    //wnin���������ϵ�½��ٶ�
    //wnin=wnen+wnie
	double wnie[3],wnen[3],wnin[3];
	double wnien[3];//wnie+wnin
	//double g;
	double gn[3];
	double gcc[3];

}_EARTH_t;
//SINS////////////////////////////////////////////////////
typedef struct
{	
	double att[3];	  //��̬ ���������������,��λrad
    double att_pre[3];	
	double vn[3];	   //��Ե�������ϵ���ٶ�
	double pos[3];	  //λ�� γ(rad)����(rad)���ߣ�����m��
	double pos_pre[3];	  //λ�� γ(rad)����(rad)���ߣ�����m��
	double wb_ib[3];//iϵ�²�����Ľ��ٶ�
	double fb_ib[3];	  //iϵ�²�����ļӼ�m/s2
//	double wb[3];	   //��������
//	double fb[3];	  //�ӱ�����
    double Cb2n[3*3],Cn2b[3*3];
    double qnb[4];	
	double eb[3];	   //������ƫ,��λrad/s
	double db[3];	   //�ӱ���ƫ,��λm/s2
	double es;		   //���ݱ����������
	double gyro[3],acc[3];           //ʹ�õ����ݡ��ӱ�����
	double gyro_pre[3],acc_pre[3];   //ʹ�õ����ݡ��ӱ���ʷ����
	double gyro_off[3];
	double acc_off[3];
	double fn[3];//����ϵ����������λm/s2, ���ϵ�ǰ�������ٶȾ��ǵ�ǰ���ٶ�
    double an[3];//����ϵ�µ�ǰ���ٶȵ�λm/s2
	double web[3],wnb[3], wnb_pre[3];//web����������ϵ�µĽ��ٶ�,wnb����ϵ�µĽ��ٶ�
	unsigned char Init_flag;
	double ts, nts;//ts�Ǵ������Ĳ������ڣ�nts���㷨�����Ĳ�������
	double Mpv[9];//λ�ø��¾���
//	_Mat_t Cnb,Cbn;
//	_Q_t qnb;
	double q_Norm;
	double q_Norm_f;
	double dtheta[3];
	double ods2gnss;//�����Ǻͺ���ǵĲ�ֵ
	double ods2gnssfilter;
}_SINS_t;

//SINS_buffer////////////////////////////////////////////////////
typedef struct
{	
	double att[3];	  //��̬ ���������������	
	double vn[3];	   //��Ե�������ϵ���ٶ�
	double pos[3];	  //λ�� γ�������ߣ����Σ�
    double qnb[4];	
    double wnb[3];    
	double Mpv[9];	
	double an[3];
	double WheelSpeed_ave;//ƽ������
	double M_vn[3];
	double M_an[3];
}_SINS_BUFFER_t;


//ODS////////////////////////////////////////////////////
typedef struct
{
	unsigned int counter;                   /*������ʱ���*/
	unsigned int counter_old;
	float timestamp;					/* ʱ���, ��λ:s , ���ȣ�0.0001*/
	float timestamp_old; 
	double WheelSpeed_ave;				/* M/S */
//	double WheelSpeed_buffer[WHEEL_BUFFER_SIZE];		//�������20����������
	//unsigned char wheelHead;			//������ָ��λ��
    float WheelSpeed_Front_Left;		/* ���� ��ǰ, ��λ: m/s, ���ȣ�����*/    
    float WheelSpeed_Back_Left;			/* ���� ���, ��λ: m/s, ���ȣ�����*/
    float WheelSpeed_Front_Right;		/* ���� ��ǰ, ��λ: m/s, ���ȣ�����*/
    float WheelSpeed_Back_Right;		/* ���� �Һ�, ��λ: m/s, ���ȣ�����*/
    float WheelSteer;					/* ������, ��λ: ��, ���ȣ�����*/
    float OdoPulse_1;					/* ��̼�����, ��λ:  ����, ����: ���� */
    float OdoPulse_2;					/* ��̼�����, ��λ:  ����, ����: ���� */
    unsigned char Gear;						/* ������λ */

	double att_ods2_b[3];
	double att_ods2_b_filte[3];
	
	double scale_factor;
	double scale_factor_filte;
	double  P_wheel_fact;
	//double scale_factor_filte_RTK;//rtk���ź�ʱ���Ƶ����ٱ���
	unsigned char ods_flag;  //1:�����ټ�  0�������ټ�
	unsigned char ods_caculat_flag;
	unsigned char ods_W_flag; //*******
		unsigned char ods_normal_flag; //***�������٣������Ƿ��쳣**20241230****0:�쳣��1������****
	float    deltaT;
	double ODS_anglespeed;
 
}_OD_t;
//**********20240713*****Guolong Zhang*******sub kf********
typedef struct
{
	double Xk[3];
	double Pk[3*3];
	double Qk[3*3];
	double Fk[3*3];
	double Rk[3];
	double Xk_[3];
	double att_xyz[3];//****rad******IMU�복����װ��***
	double Q_b2m[4];
	double Cmb[3*3];
	double att_b2gps[3];//****b��gps�İ�װ��**rad**
	double P_b2gps;
	//*******subkf********1104����*******
	double fog_kz;
	double Pk_fog;
	double Bias_gz;
	unsigned int Fog_Cnt;
	unsigned char Checkfog_flag;//*********��ɹ��Ƶı�־λ*****
	//
}_Sub_KF;
//**********20240713*****Guolong Zhang*******sub kf********
//KF////////////////////////////////////////////////////
typedef struct
{
	//15�����ֱ�Ϊ��̬����(ʧ׼��)���ٶ���ά�Ⱦ��ȸ߳���������ƫ���Ӽ���ƫ���
	double Xk[NA];
	double Xk_[NA];
	double Zk[7];//�۲�����ESKFΪ�۲����в�ٶ�(m/s2,m/s2,m/s2),λ��(rad,rad,m),����(rad)
	double Qk[NA*NA];
	double RK[7];
	double Fk[NA*NA];//̬���ٶ���λ�������ٶȼ���ƫ����������ƫ
	double Pxk[NA*NA];//p k ;  p k-1
	double Pxk_[NA*NA];//p k,k-1
	double Kk[NA*7];	
	double P_max[NA];
	double P_min[NA];
	unsigned char measure_flag;
	unsigned char measure_flag_vn;
	unsigned char measure_flag_pos;
	unsigned char measure_flag_head;  //1����˫���ߺ���� 0������˫���ߺ����
	unsigned char RTK_Heading_OK;
    unsigned char use_gps_flag; //1����GPS 0:����gps�� ���õ��ں�״̬
	unsigned char measure_flag_NHC;
	unsigned char measure_flag_ZUPT;
	unsigned char measure_flag_Wheel;
	
	unsigned char pre_fusion_source;
	unsigned char fusion_source; //1��gps 2������ �� ʵ�ʵ��ں�״̬
	
	
	unsigned int cunt;
	unsigned char step;
}_KF_t;


//����������
typedef struct
{
	double mag_use[3];
}_MAGNET_T;


typedef struct
{
	_IMU_t IMU;
	_GPS_t GPS;
	_MAGNET_T MAGNET;
	_Param_t Param;
	
	_EARTH_t EARTH;
	_SINS_t SINS;
	_SINS_BUFFER_t SINS_buffer[SINS_BUFFER_SIZE];
	unsigned char Head,end;
	_OD_t ODS;
	_KF_t KF;
	_Sub_KF  SubKF;
	unsigned char pre_Nav_Standard_flag;//��һ���궨״̬
	unsigned char Nav_Standard_flag;  //0:�궨δ���  1���궨���
	unsigned char pre_Nav_Status;//�ϸ�״̬
	unsigned char Nav_Status;  //0: ����׼��  1��SINS��ʼ��׼ 2��SINS KF��ʼ�� 3��ϵͳ�궨 4���������� 5��ֹͣ����
	unsigned char Gnss_Use_Status;//E_NAV_SUPPORT_RTK_FIEX_STATUS:ֻ֧��rtk����й۲⣻E_NAV_SUPPORT_RTK_ALL_STATUS:֧��SPP��DGPS��FIEXED��FLOAT���Լ��

	unsigned short debug: 1;		//0:normal 1:debug mode
    unsigned short imuSelect: 1;	//0:mems 1:ifog
	unsigned short memsType: 2;	//0: imu460 1:scha63x

	unsigned char  UseCase;		//ʹ�ó�����0��Ĭ�ϳ���ʹ������+GNSS+ODS 1:�������˻�
  unsigned char  ZUPT_flag;		//0,FALSE; 1,TRUE
		unsigned int  ZUPTyaw_ST_Cnt;//*****�ü���������flag���л����ж�**20241021******
		unsigned int  ZUPT_hold_cnt;  //零速约束持续计数器，用于GNSS失锁后延长零速约束
 // unsigned char  CarODO;		//0,FALSE; 1,TRUE
//  unsigned short IMU_Cnt;  
	unsigned short  acc_gyr_Cnt;
	unsigned int  Car_SOURCE_UP_Cur_ms;//****���ڼ����ODS��������¶�Ӧ��ʱ��******
  double buffer_acc[ZUPT_SIZE];
	 double buffer_gyr[ZUPT_SIZE];
	unsigned char ins_buffer_full_flag;
	unsigned int    ins_buffer_full_cnt;//������������Ժ����
	unsigned char TurnningFlag;
	double   Modacc;//g
	double   Modgyr;//deg/s
	double   Modacc_std2;
	double   Modgyr_std2;
			double Macc[3];//���ڼӱ���ֵ��������ֵ
			double Ma_x[ZUPT_SIZE];
			double Ma_y[ZUPT_SIZE];
			double Ma_z[ZUPT_SIZE];
			double Mgyr[3];//�������ݾ�ֵ��������ֵ
			double Mg_x[ZUPT_SIZE];
			double Mg_y[ZUPT_SIZE];
			double Mg_z[ZUPT_SIZE];
    double Q_body[4];
	double att_body[3];//******������̬����Ԫ��****
	int KF_CheckCnt ;//*****���ڱ궨����*****
	int Subkf2_Cnt;//*****subkf2����������******
	//*******OUTAGE SIMULATE****
	//double outage_start;
	double outage_start_per[outnum];
	double difAng;
	_calib_t				gyroCalib;
	_calib_t				accCalib;
	//**********just for test*****
 //GPS���ݳ�ʼ��֮ǰ����̬���
 double Pre_att[3];//xyz����
	unsigned char  Pre_att_flag;		//0:δ������ϣ����IMUˮƽ��̬��  1��������ϣ���������̬
			//****��װ�������******
	char Axis[3];//******XYZ�����****
	float Axis_sign[3];//******XYZ��ϵķ���****
	unsigned char Axis_flag;//******��λ���Ƿ���������Ч����****
	unsigned int GPSlastnum;//����������ʼ�׶ε�gps����
}_NAV_Data_Full_t;

typedef struct
{
	unsigned char  fusion_source;
	unsigned int   duration;
}_NAV_Funsion_Status_Time_t;

//IIR�˲����ṹ��
typedef struct
{
	double a;
	double b;
	double x;
	double y;
}_IIR_Filter_t;

//�����˲��ṹ��
typedef struct
{
	_IIR_Filter_t  IIR_Filter;
	unsigned char  adaptive;//�Ƿ��������Ӧ��ʽ0�������ã�1������
	double Kp;
	double Ki;
}_NAV_MAHONY_t;





#endif 



