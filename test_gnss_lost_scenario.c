#include <stdio.h>
#include <math.h>

// 模拟修改后的零速约束逻辑
#define RETURN_SUCESS 1
#define RETURN_FAIL 0
#define ZUPT_HOLD_COUNT 200  // 2秒持续时间

// 模拟的阈值
#define TH_acc_NORMAL (5*0.542*0.001)
#define TH_gyr_NORMAL (5*0.013)
#define TH_acc_GNSS_LOST (20*0.542*0.001)
#define TH_gyr_GNSS_LOST (20*0.013)
#define SPEED_THRESHOLD_GNSS_LOST 1.0

typedef struct {
    int ZUPT_flag;
    int ZUPT_hold_cnt;
    double vn[3];  // 速度
    int gnss_lost;
    double acc_std;
    double gyr_std;
} SimData;

// 模拟零速约束检测函数
int simulate_zupt_detection(SimData* data) {
    double th_acc_use = TH_acc_NORMAL;
    double th_gyr_use = TH_gyr_NORMAL;
    
    // 计算速度模长
    double speed = sqrt(data->vn[0]*data->vn[0] + data->vn[1]*data->vn[1] + data->vn[2]*data->vn[2]);

    if(data->gnss_lost) {
        th_acc_use = TH_acc_GNSS_LOST;
        th_gyr_use = TH_gyr_GNSS_LOST;

        // GNSS失锁时的智能零速约束策略
        // 策略1: 满足严格条件且速度合理时，应用零速约束
        if (data->acc_std < th_acc_use && data->gyr_std < th_gyr_use && speed < SPEED_THRESHOLD_GNSS_LOST) {
            data->ZUPT_flag = RETURN_SUCESS;
            data->ZUPT_hold_cnt = ZUPT_HOLD_COUNT;
        }
        // 策略2: 速度小于1.0m/s且IMU相对稳定时，应用零速约束
        else if(speed < SPEED_THRESHOLD_GNSS_LOST &&
                data->acc_std < 3*th_acc_use && data->gyr_std < 3*th_gyr_use) {
            data->ZUPT_flag = RETURN_SUCESS;
            data->ZUPT_hold_cnt = ZUPT_HOLD_COUNT;
        }
        // 策略3: 速度较小且加速度非常稳定时，强制零速约束
        else if(speed < 0.5 && data->acc_std < th_acc_use) {
            data->ZUPT_flag = RETURN_SUCESS;
            data->ZUPT_hold_cnt = ZUPT_HOLD_COUNT/2;
        }
        else {
            // 检查是否在持续期内
            if(data->ZUPT_hold_cnt > 0) {
                data->ZUPT_flag = RETURN_SUCESS;  // 继续保持零速约束
                data->ZUPT_hold_cnt--;
            }
            else {
                data->ZUPT_flag = RETURN_FAIL;
            }
        }
    }
    else {
        // GNSS正常时使用原有逻辑
        if (data->acc_std < th_acc_use && data->gyr_std < th_gyr_use) {
            data->ZUPT_flag = RETURN_SUCESS;
        }
        else {
            data->ZUPT_flag = RETURN_FAIL;
            data->ZUPT_hold_cnt = 0;
        }
    }
    
    return data->ZUPT_flag;
}

int main() {
    SimData data = {0};
    
    printf("模拟GNSS失锁场景测试:\n");
    printf("正常阈值: acc=%.6f, gyr=%.6f\n", TH_acc_NORMAL, TH_gyr_NORMAL);
    printf("失锁阈值: acc=%.6f, gyr=%.6f\n", TH_acc_GNSS_LOST, TH_gyr_GNSS_LOST);
    printf("速度阈值: %.1f m/s\n\n", SPEED_THRESHOLD_GNSS_LOST);
    
    // 场景1: GNSS正常工作，设备静止
    printf("=== 场景1: GNSS正常，设备静止 ===\n");
    data.gnss_lost = 0;
    data.vn[0] = 0.01; data.vn[1] = 0.01; data.vn[2] = 0.0;
    data.acc_std = 0.002; data.gyr_std = 0.005;
    data.ZUPT_flag = 0; data.ZUPT_hold_cnt = 0;
    int result = simulate_zupt_detection(&data);
    printf("速度: %.3f m/s, ZUPT: %d, hold_cnt: %d\n\n", 
           sqrt(data.vn[0]*data.vn[0] + data.vn[1]*data.vn[1]), result, data.ZUPT_hold_cnt);
    
    // 场景2: GNSS失锁，设备静止
    printf("=== 场景2: GNSS失锁，设备静止 ===\n");
    data.gnss_lost = 1;
    data.vn[0] = 0.05; data.vn[1] = 0.03; data.vn[2] = 0.0;
    data.acc_std = 0.008; data.gyr_std = 0.02;
    data.ZUPT_flag = 0; data.ZUPT_hold_cnt = 0;
    result = simulate_zupt_detection(&data);
    printf("速度: %.3f m/s, ZUPT: %d, hold_cnt: %d\n\n", 
           sqrt(data.vn[0]*data.vn[0] + data.vn[1]*data.vn[1]), result, data.ZUPT_hold_cnt);
    
    // 场景3: GNSS失锁，速度0.9m/s (您遇到的情况)
    printf("=== 场景3: GNSS失锁，速度0.9m/s (实际遇到的情况) ===\n");
    data.gnss_lost = 1;
    data.vn[0] = 0.9; data.vn[1] = 0.1; data.vn[2] = 0.0;
    data.acc_std = 0.008; data.gyr_std = 0.02;
    data.ZUPT_flag = 0; data.ZUPT_hold_cnt = 0;
    result = simulate_zupt_detection(&data);
    printf("速度: %.3f m/s, ZUPT: %d, hold_cnt: %d\n", 
           sqrt(data.vn[0]*data.vn[0] + data.vn[1]*data.vn[1]), result, data.ZUPT_hold_cnt);
    printf("分析: 速度0.9m/s < 1.0m/s阈值，IMU相对稳定，应该应用零速约束\n\n");
    
    // 场景4: GNSS失锁，速度较小但加速度很稳定
    printf("=== 场景4: GNSS失锁，速度0.4m/s，加速度很稳定 ===\n");
    data.gnss_lost = 1;
    data.vn[0] = 0.3; data.vn[1] = 0.2; data.vn[2] = 0.0;
    data.acc_std = 0.003; data.gyr_std = 0.025;
    data.ZUPT_flag = 0; data.ZUPT_hold_cnt = 0;
    result = simulate_zupt_detection(&data);
    printf("速度: %.3f m/s, ZUPT: %d, hold_cnt: %d\n", 
           sqrt(data.vn[0]*data.vn[0] + data.vn[1]*data.vn[1]), result, data.ZUPT_hold_cnt);
    printf("分析: 速度<0.5m/s且加速度很稳定，强制零速约束\n\n");
    
    // 场景5: GNSS失锁，速度过大
    printf("=== 场景5: GNSS失锁，速度1.5m/s，不应该零速约束 ===\n");
    data.gnss_lost = 1;
    data.vn[0] = 1.2; data.vn[1] = 0.8; data.vn[2] = 0.0;
    data.acc_std = 0.008; data.gyr_std = 0.02;
    data.ZUPT_flag = 0; data.ZUPT_hold_cnt = 0;
    result = simulate_zupt_detection(&data);
    printf("速度: %.3f m/s, ZUPT: %d, hold_cnt: %d\n", 
           sqrt(data.vn[0]*data.vn[0] + data.vn[1]*data.vn[1]), result, data.ZUPT_hold_cnt);
    printf("分析: 速度>1.0m/s，不应该应用零速约束\n\n");
    
    printf("=== 总结 ===\n");
    printf("修改后的逻辑能够:\n");
    printf("1. 在GNSS失锁且速度较小时有效应用零速约束\n");
    printf("2. 避免在高速运动时错误约束\n");
    printf("3. 通过多重条件判断提高鲁棒性\n");
    
    return 0;
}
