<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\arm2.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\arm2.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Mon Jun 16 19:47:54 2025
<BR><P>
<H3>Maximum Stack Usage =       2544 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; NAV_function &rArr; NavKalmanMode &rArr; KF_UP2 &rArr; ODS_Angle_Estimation &rArr; qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[8]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">SVC_Handler</a><BR>
 <LI><a href="#[a]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">PendSV_Handler</a><BR>
 <LI><a href="#[22]">CAN0_EWMC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[22]">CAN0_EWMC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1e]">ADC_IRQHandler</a> from gd32f4xx_it.o(i.ADC_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">CAN0_RX0_IRQHandler</a> from bsp_can.o(i.CAN0_RX0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">CAN1_RX0_IRQHandler</a> from bsp_can.o(i.CAN1_RX0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1c]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1d]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[9]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">EXTI10_15_IRQHandler</a> from gd32f4xx_it.o(i.EXTI10_15_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">EXTI3_IRQHandler</a> from gd32f4xx_it.o(i.EXTI3_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">EXTI5_9_IRQHandler</a> from gd32f4xx_it.o(i.EXTI5_9_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[10]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[d]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[a]">PendSV_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[f]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">SDIO_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8]">SVC_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[b]">SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[66]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[e]">TAMPER_STAMP_IRQHandler</a> from bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">TIMER0_UP_TIMER9_IRQHandler</a> from gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">TIMER1_IRQHandler</a> from gd32f4xx_it.o(i.TIMER1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">TIMER2_IRQHandler</a> from gd32f4xx_it.o(i.TIMER2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">TIMER3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">TIMER6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">UART4_IRQHandler</a> from gd32f4xx_it.o(i.UART4_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">UART6_IRQHandler</a> from gd32f4xx_it.o(i.UART6_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">USART0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">USART2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[c]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[67]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[69]">_sbackspace</a> from _sgetc.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[68]">_sgetc</a> from _sgetc.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[6c]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[6f]">bmp2_delay_us</a> from common.o(i.bmp2_delay_us) referenced from common.o(i.bmp2_interface_selection)
 <LI><a href="#[6d]">bmp2_i2c_read</a> from common.o(i.bmp2_i2c_read) referenced from common.o(i.bmp2_interface_selection)
 <LI><a href="#[6e]">bmp2_i2c_write</a> from common.o(i.bmp2_i2c_write) referenced from common.o(i.bmp2_interface_selection)
 <LI><a href="#[6b]">fputc</a> from main.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[6a]">isspace</a> from isspace_o.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[65]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[67]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[253]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[70]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[96]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[254]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[255]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[256]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[257]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[258]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[2]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_EWMC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_EWMC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e1]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDataHandle
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_Output
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart6sendmsg
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateFirmsendmsg
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcusendtopcdriversdata
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NavStandardParm2Flash
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliWriteConfig
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateStop_SetEnd
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateStart_SetEnd
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateSend_SetEnd
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateEnd_SetEnd
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaVector
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStop
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStart
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateEnd
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaTime
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanR
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanQ
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGyroType
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGpsType
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnssInitValue
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnss
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFrequency
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFilter
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorGyro
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorAcc
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDeviation
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDebugMode
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDataOutType
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCoord
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCalibration
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAngle
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAll
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParaToFlash
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RestoreFactory
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_4
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_3
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_2
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_1
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_0
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara4_SetEnd
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara3_SetEnd
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara2_SetEnd
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara1_SetEnd
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara0_SetEnd
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_nav_para_syn
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adj_paraSyn
</UL>

<P><STRONG><a name="[9f]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_compute_meas_time
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_info_get
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_UpdateFirm_nav
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_UpdateFirm
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Calib_Parms
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaVector
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStop
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStart
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateEnd
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaTime
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanR
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanQ
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGyroType
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGpsType
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnssInitValue
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnss
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFrequency
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFilter
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorGyro
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorAcc
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDeviation
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDebugMode
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDataOutType
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCoord
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCalibration
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAngle
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAll
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParaToFlash
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RestoreFactory
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_4
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_3
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_2
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_1
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_0
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_resume_defaultPara
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_fm_update
</UL>

<P><STRONG><a name="[259]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[73]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[25a]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[25b]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[72]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initializationdriversettings
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDataHandle
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
<LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RestoreFactory
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>

<P><STRONG><a name="[d4]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateFirmsendmsg
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitCH395InfParam
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_calib_param
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_UpdateFirm
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WheelObsCalAndNoiseSet
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPSObsCalAndNoiseSet
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KfFeedback
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_Init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowNavStatus
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowHelp
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowErrorCmd
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowBuildVersion
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliSetRestartNav
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliReadConfig
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateStop_SetEnd
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateStart_SetEnd
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateSend_SetEnd
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateEnd_SetEnd
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaVector
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStop
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStart
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateEnd
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaTime
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanR
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanQ
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGyroType
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGpsType
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnssInitValue
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnss
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFrequency
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFilter
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorGyro
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorAcc
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDeviation
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDebugMode
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDataOutType
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCoord
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCalibration
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAngle
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAll
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParaToFlash
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RestoreFactory
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_4
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_3
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_2
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_1
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_0
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara4_SetEnd
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara3_SetEnd
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara2_SetEnd
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara1_SetEnd
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara0_SetEnd
</UL>

<P><STRONG><a name="[25c]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[74]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[d6]"></a>strcat</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, strcat.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowNavStatus
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowHelp
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowErrorCmd
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowBuildVersion
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliReadConfig
</UL>

<P><STRONG><a name="[de]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliWriteConfig
</UL>

<P><STRONG><a name="[208]"></a>strncpy</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_fm_update
</UL>

<P><STRONG><a name="[d7]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowNavStatus
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowHelp
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowErrorCmd
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowBuildVersion
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliReadConfig
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
</UL>

<P><STRONG><a name="[22c]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initializationdriversettings
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
</UL>

<P><STRONG><a name="[23c]"></a>strtok</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, strtok.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strtok
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_split_fnum
</UL>

<P><STRONG><a name="[90]"></a>_scanf_real</STRONG> (Thumb, 0 bytes, Stack size 104 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>

<P><STRONG><a name="[79]"></a>_scanf_really_real</STRONG> (Thumb, 556 bytes, Stack size 104 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[7c]"></a>atoi</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, atoi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliWriteConfig
</UL>

<P><STRONG><a name="[7f]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_Output
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;symmetry
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WheelSpeedOptimize
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat3_Inv
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WheelObsCalAndNoiseSet
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KfFeedback
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_ODS_Data
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_GNSS_Data
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading_PI
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scalar_KalmanFilte
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rv2q
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnbmul
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InnerDot
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>

<P><STRONG><a name="[84]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat3_Inv
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat2_Inv
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WheelObsCalAndNoiseSet
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ObsCalAndObsNoiseSet
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CheckStandardCompleteStatus
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KfFeedback
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_ODS_Data
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_GNSS_Data
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading_PI
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scalar_KalmanFilte
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Caculate_ODS_angle
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnbmul
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cross3
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[85]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rv2q
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[77]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_Output
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calcGPRMC_TRA
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;symmetry
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat3_Inv
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat2_Inv
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WheelObsCalAndNoiseSet
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetGnssKalmanRmatrix
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ObsCalAndObsNoiseSet
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NavStandardParm2Flash
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPSObsCalAndNoiseSet
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KfFeedback
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_Init
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_ODS_Data
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_GNSS_Data
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Out_Data_Up
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scalar_KalmanFilte
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Caculate_ODS_angle
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rv2q
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnbmul
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cross3
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InnerDot
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[78]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_Output
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WheelSpeedOptimize
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat3_Inv
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat2_Inv
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_Init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Out_Data_Up
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scalar_KalmanFilte
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Caculate_ODS_angle
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rv2q
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
</UL>

<P><STRONG><a name="[86]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calcGPRMC_TRA
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[87]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KfFeedback
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[88]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_Output
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[8a]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>

<P><STRONG><a name="[cf]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calcGPRMC_TRA
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetGnssKalmanRmatrix
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Param_Data
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_ODS_Data
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_GNSS_Data
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliWriteConfig
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliReadConfig
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Caculate_ODS_angle
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
</UL>

<P><STRONG><a name="[140]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat3_Inv
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat2_Inv
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>

<P><STRONG><a name="[d2]"></a>__aeabi_cdcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ObsCalAndObsNoiseSet
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CheckStandardCompleteStatus
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Standard_Data
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_ODS_Data
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_GNSS_Data
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading_PI
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rv2q
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[9b]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetGnssKalmanRmatrix
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ObsCalAndObsNoiseSet
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CheckStandardCompleteStatus
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_ODS_Data
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading_PI
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gyro_data_check
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Acc_data_check
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scalar_KalmanFilte
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[7b]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calcGPRMC_TRA
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_split_fnum
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[25d]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[1d9]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[8c]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[80]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[25e]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[89]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[25f]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[81]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[260]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[8d]"></a>localtime</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, localtime_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = localtime &rArr; _localtime
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_localtime
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
</UL>

<P><STRONG><a name="[8e]"></a>_localtime</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, localtime_i.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _localtime
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;localtime
</UL>

<P><STRONG><a name="[68]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[69]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[91]"></a>__strtod_int</STRONG> (Thumb, 90 bytes, Stack size 40 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[7e]"></a>strtol</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, strtol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[261]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[8b]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[262]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[83]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[82]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
</UL>

<P><STRONG><a name="[1d4]"></a>__ARM_scalbn</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, dscalb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[263]"></a>scalbn</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, dscalb.o(.text), UNUSED)

<P><STRONG><a name="[94]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[76]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[95]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[71]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[264]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[92]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[6a]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace_o.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = isspace
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[93]"></a>_strtoul</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, _strtoul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[97]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
</UL>

<P><STRONG><a name="[265]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[266]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[1e]"></a>ADC_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[99]"></a>Acc_data_check</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, nav_imu.o(i.Acc_data_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Acc_data_check &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
</UL>

<P><STRONG><a name="[9c]"></a>Arm_SendMsg</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, nav_cli.o(i.Arm_SendMsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = Arm_SendMsg &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowNavStatus
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowHelp
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowErrorCmd
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowBuildVersion
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliReadConfig
</UL>

<P><STRONG><a name="[6]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 904 bytes, Stack size 8 bytes, bsp_can.o(i.CAN0_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CAN0_RX0_IRQHandler &rArr; can_message_receive
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_message_receive
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, bsp_can.o(i.CAN1_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CAN1_RX0_IRQHandler &rArr; can_message_receive
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_message_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a2]"></a>CH378ByteLocate</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, file_sys.o(i.CH378ByteLocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = CH378ByteLocate &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[a6]"></a>CH378ByteWrite</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, file_sys.o(i.CH378ByteWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = CH378ByteWrite &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
</UL>

<P><STRONG><a name="[a8]"></a>CH378FileCreate</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, file_sys.o(i.CH378FileCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = CH378FileCreate &rArr; CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[ab]"></a>CH378FileOpen</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, file_sys.o(i.CH378FileOpen))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = CH378FileOpen &rArr; CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[ac]"></a>CH378GetDiskStatus</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, file_sys.o(i.CH378GetDiskStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH378GetDiskStatus &rArr; CH378ReadVar8 &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[ae]"></a>CH378GetICVer</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, file_sys.o(i.CH378GetICVer))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = CH378GetICVer &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
</UL>

<P><STRONG><a name="[b0]"></a>CH378GetIntStatus</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, file_sys.o(i.CH378GetIntStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>

<P><STRONG><a name="[ad]"></a>CH378ReadVar8</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, file_sys.o(i.CH378ReadVar8))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CH378ReadVar8 &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetDiskStatus
</UL>

<P><STRONG><a name="[aa]"></a>CH378SendCmdWaitInt</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, file_sys.o(i.CH378SendCmdWaitInt))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileOpen
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileCreate
</UL>

<P><STRONG><a name="[a9]"></a>CH378SetFileName</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, file_sys.o(i.CH378SetFileName))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CH378SetFileName &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileOpen
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileCreate
</UL>

<P><STRONG><a name="[a7]"></a>CH378WriteOfsBlock</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, file_sys.o(i.CH378WriteOfsBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH378WriteOfsBlock &rArr; CH378_mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
</UL>

<P><STRONG><a name="[b2]"></a>CH378_Port_Init</STRONG> (Thumb, 230 bytes, Stack size 32 bytes, ch378_spi_hw.o(i.CH378_Port_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = CH378_Port_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_crc_off
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
</UL>

<P><STRONG><a name="[ba]"></a>CH378_mDelaymS</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ch378_hal.o(i.CH378_mDelaymS))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CH378_mDelaymS
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[b1]"></a>CH378_mDelayuS</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ch378_hal.o(i.CH378_mDelayuS))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = CH378_mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
</UL>

<P><STRONG><a name="[bd]"></a>CH395CMDCheckExist</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, ch395cmd.o(i.CH395CMDCheckExist))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH395CMDCheckExist &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Data
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH395Data
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395Init
</UL>

<P><STRONG><a name="[c1]"></a>CH395CMDInitCH395</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, ch395cmd.o(i.CH395CMDInitCH395))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = CH395CMDInitCH395 &rArr; CH395GetCmdStatus &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelaymS
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395GetCmdStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395Init
</UL>

<P><STRONG><a name="[c4]"></a>CH395CMDSetGWIPAddr</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ch395cmd.o(i.CH395CMDSetGWIPAddr))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH395CMDSetGWIPAddr &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Data
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395Init
</UL>

<P><STRONG><a name="[c5]"></a>CH395CMDSetIPAddr</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ch395cmd.o(i.CH395CMDSetIPAddr))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH395CMDSetIPAddr &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Data
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395Init
</UL>

<P><STRONG><a name="[c6]"></a>CH395CMDSetMASKAddr</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ch395cmd.o(i.CH395CMDSetMASKAddr))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH395CMDSetMASKAddr &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Data
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395Init
</UL>

<P><STRONG><a name="[c3]"></a>CH395GetCmdStatus</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, ch395cmd.o(i.CH395GetCmdStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CH395GetCmdStatus &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH395Data
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDInitCH395
</UL>

<P><STRONG><a name="[c7]"></a>CH395Init</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, tcpserver.o(i.CH395Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = CH395Init &rArr; CH395CMDInitCH395 &rArr; CH395GetCmdStatus &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelaymS
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetStartPara
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetMASKAddr
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetIPAddr
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetGWIPAddr
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDInitCH395
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDCheckExist
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TCPServer_Init
</UL>

<P><STRONG><a name="[c9]"></a>CH395SetSocketRecvBuf</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, ch395cmd.o(i.CH395SetSocketRecvBuf))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH395SetSocketRecvBuf &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Data
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket_buffer_config
</UL>

<P><STRONG><a name="[ca]"></a>CH395SetSocketSendBuf</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, ch395cmd.o(i.CH395SetSocketSendBuf))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH395SetSocketSendBuf &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Data
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket_buffer_config
</UL>

<P><STRONG><a name="[c8]"></a>CH395SetStartPara</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, ch395cmd.o(i.CH395SetStartPara))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CH395SetStartPara &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Data
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395Init
</UL>

<P><STRONG><a name="[cb]"></a>CH395SetTCPMss</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, ch395cmd.o(i.CH395SetTCPMss))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CH395SetTCPMss &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Data
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TCPServer_Init
</UL>

<P><STRONG><a name="[cc]"></a>CH395_PORT_INIT</STRONG> (Thumb, 198 bytes, Stack size 32 bytes, ch395spi.o(i.CH395_PORT_INIT))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = CH395_PORT_INIT &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_crc_off
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TCPServer_Init
</UL>

<P><STRONG><a name="[cd]"></a>CH395_RST</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, ch395spi.o(i.CH395_RST))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CH395_RST &rArr; mDelaymS
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelaymS
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TCPServer_Init
</UL>

<P><STRONG><a name="[ce]"></a>Caculate_ODS_angle</STRONG> (Thumb, 138 bytes, Stack size 48 bytes, nav_ods.o(i.Caculate_ODS_angle))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Caculate_ODS_angle &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_ODS_Data
</UL>

<P><STRONG><a name="[d0]"></a>CheckStandardCompleteStatus</STRONG> (Thumb, 2188 bytes, Stack size 16 bytes, nav_kf.o(i.CheckStandardCompleteStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = CheckStandardCompleteStatus &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
</UL>

<P><STRONG><a name="[1b8]"></a>CheckSum</STRONG> (Thumb, 124 bytes, Stack size 36 bytes, firmwareupdatefile.o(i.CheckSum))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CheckSum
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_UpdateFirm_nav
</UL>

<P><STRONG><a name="[d3]"></a>CliReadConfig</STRONG> (Thumb, 2018 bytes, Stack size 2224 bytes, nav_cli.o(i.CliReadConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 2288<LI>Call Chain = CliReadConfig &rArr; Arm_SendMsg &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Arm_SendMsg
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliWriteConfig
</UL>

<P><STRONG><a name="[d8]"></a>CliSetRestartNav</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, nav_cli.o(i.CliSetRestartNav))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CliSetRestartNav
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
</UL>

<P><STRONG><a name="[153]"></a>CliSetStopNav</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, nav_cli.o(i.CliSetStopNav))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
</UL>

<P><STRONG><a name="[151]"></a>CliShowAbormal</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, nav_cli.o(i.CliShowAbormal))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
</UL>

<P><STRONG><a name="[d9]"></a>CliShowBuildVersion</STRONG> (Thumb, 72 bytes, Stack size 136 bytes, nav_cli.o(i.CliShowBuildVersion))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = CliShowBuildVersion &rArr; Arm_SendMsg &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Arm_SendMsg
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
</UL>

<P><STRONG><a name="[da]"></a>CliShowErrorCmd</STRONG> (Thumb, 74 bytes, Stack size 1168 bytes, nav_cli.o(i.CliShowErrorCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 1232<LI>Call Chain = CliShowErrorCmd &rArr; Arm_SendMsg &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Arm_SendMsg
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliWriteConfig
</UL>

<P><STRONG><a name="[db]"></a>CliShowHelp</STRONG> (Thumb, 482 bytes, Stack size 2184 bytes, nav_cli.o(i.CliShowHelp))
<BR><BR>[Stack]<UL><LI>Max Depth = 2248<LI>Call Chain = CliShowHelp &rArr; Arm_SendMsg &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Arm_SendMsg
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
</UL>

<P><STRONG><a name="[152]"></a>CliShowNavSet</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, nav_cli.o(i.CliShowNavSet))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
</UL>

<P><STRONG><a name="[dc]"></a>CliShowNavStatus</STRONG> (Thumb, 206 bytes, Stack size 1160 bytes, nav_cli.o(i.CliShowNavStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 1224<LI>Call Chain = CliShowNavStatus &rArr; Arm_SendMsg &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Arm_SendMsg
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
</UL>

<P><STRONG><a name="[dd]"></a>CliWriteConfig</STRONG> (Thumb, 902 bytes, Stack size 32 bytes, nav_cli.o(i.CliWriteConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 2320<LI>Call Chain = CliWriteConfig &rArr; CliReadConfig &rArr; Arm_SendMsg &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_saveCaliData
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowErrorCmd
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliReadConfig
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_split_fnum
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
</UL>

<P><STRONG><a name="[e4]"></a>CorrHeading</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, nav_app.o(i.CorrHeading))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = CorrHeading &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_GNSS_Data
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[e5]"></a>CorrHeading_PI</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, nav_app.o(i.CorrHeading_PI))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = CorrHeading_PI &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
</UL>

<P><STRONG><a name="[f0]"></a>DRam_Read</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, bsp_fmc.o(i.DRam_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DRam_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[9]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e6]"></a>Drv_FlashErase</STRONG> (Thumb, 76 bytes, Stack size 56 bytes, firmwareupdatefile.o(i.Drv_FlashErase))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Drv_FlashErase &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_flag_clear
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_info_get
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateFileHandle
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashWrite
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParaToFlash
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RestoreFactory
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParaUpdateHandle
</UL>

<P><STRONG><a name="[163]"></a>Drv_FlashRead_8bit</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, firmwareupdatefile.o(i.Drv_FlashRead_8bit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Drv_FlashRead_8bit
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
</UL>

<P><STRONG><a name="[ec]"></a>Drv_FlashWrite</STRONG> (Thumb, 114 bytes, Stack size 40 bytes, firmwareupdatefile.o(i.Drv_FlashWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Drv_FlashWrite &rArr; Drv_FlashErase &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_word_program
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_flag_clear
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateFileHandle
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParaToFlash
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RestoreFactory
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParaUpdateHandle
</UL>

<P><STRONG><a name="[18b]"></a>Drv_SystemReset</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, firmwareupdatefile.o(i.Drv_SystemReset))
<BR><BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDataHandle
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_UpdateFirm_nav
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateEnd
</UL>

<P><STRONG><a name="[34]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI10_15_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI10_15_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI3_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI5_9_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 992<LI>Call Chain = EXTI5_9_IRQHandler &rArr; synthesisLogBuf &rArr; time2str &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRam_Read
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16e]"></a>Earth_Init</STRONG> (Thumb, 180 bytes, Stack size 0 bytes, nav_sins.o(i.Earth_Init))
<BR><BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
</UL>

<P><STRONG><a name="[f3]"></a>Earth_UP</STRONG> (Thumb, 1294 bytes, Stack size 192 bytes, nav_sins.o(i.Earth_UP))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = Earth_UP &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cross3
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
</UL>

<P><STRONG><a name="[12c]"></a>EyeMatrix</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, nav_math.o(i.EyeMatrix))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EyeMatrix
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_Init
</UL>

<P><STRONG><a name="[f8]"></a>GPSObsCalAndNoiseSet</STRONG> (Thumb, 410 bytes, Stack size 104 bytes, nav_kf.o(i.GPSObsCalAndNoiseSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 976<LI>Call Chain = GPSObsCalAndNoiseSet &rArr; LeverarmTimeCorr2 &rArr; __hardfp_tan &rArr; __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetGnssKalmanRmatrix
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ObsCalAndObsNoiseSet
</UL>

<P><STRONG><a name="[fb]"></a>Get_GNSS_Data</STRONG> (Thumb, 1232 bytes, Stack size 56 bytes, nav_gnss.o(i.Get_GNSS_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = Get_GNSS_Data &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetNavRtkStatus
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_gnss_update
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
</UL>

<P><STRONG><a name="[100]"></a>Get_IMU_Data</STRONG> (Thumb, 9918 bytes, Stack size 560 bytes, nav_imu.o(i.Get_IMU_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 960<LI>Call Chain = Get_IMU_Data &rArr; qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetNavStatus
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Calib_Parms
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gyro_data_check
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Acc_data_check
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rv2q
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnbmul
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cross3
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
</UL>

<P><STRONG><a name="[10b]"></a>Get_ODS_Data</STRONG> (Thumb, 878 bytes, Stack size 64 bytes, nav_ods.o(i.Get_ODS_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = Get_ODS_Data &rArr; WheelSpeedOptimize &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WheelSpeedOptimize
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Caculate_ODS_angle
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
</UL>

<P><STRONG><a name="[10d]"></a>Get_Param_Data</STRONG> (Thumb, 274 bytes, Stack size 16 bytes, nav_imu.o(i.Get_Param_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Get_Param_Data
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
</UL>

<P><STRONG><a name="[102]"></a>Gyro_data_check</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, nav_imu.o(i.Gyro_data_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Gyro_data_check &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
</UL>

<P><STRONG><a name="[4]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10e]"></a>INS_Init</STRONG> (Thumb, 340 bytes, Stack size 16 bytes, main.o(i.INS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = INS_Init &rArr; comm_store_init &rArr; comm_resume_defaultPara &rArr; comm_set_customPara &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelaymS
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initializationdriversettings
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_send_end_frame
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp280_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_TxInit
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_RxInit
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TCPServer_Init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitFlashAddr
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_configuration
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[124]"></a>InitCH395InfParam</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, tcpserver.o(i.InitCH395InfParam))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = InitCH395InfParam
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TCPServer_Init
</UL>

<P><STRONG><a name="[113]"></a>InitFlashAddr</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, bsp_flash.o(i.InitFlashAddr))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[125]"></a>InitParaToAlgorithm</STRONG> (Thumb, 230 bytes, Stack size 8 bytes, setparabao.o(i.InitParaToAlgorithm))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = InitParaToAlgorithm &rArr; comm_nav_para_syn
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetCoordToAlgorithm
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_param_setbits
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_nav_para_syn
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
</UL>

<P><STRONG><a name="[129]"></a>InnerDot</STRONG> (Thumb, 106 bytes, Stack size 32 bytes, nav_math.o(i.InnerDot))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = InnerDot &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
</UL>

<P><STRONG><a name="[12a]"></a>KF_Init</STRONG> (Thumb, 4046 bytes, Stack size 72 bytes, nav_kf.o(i.KF_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = KF_Init &rArr; att2qnb &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EyeMatrix
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_XPQ
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
</UL>

<P><STRONG><a name="[12d]"></a>KF_UP2</STRONG> (Thumb, 11928 bytes, Stack size 192 bytes, nav_kf.o(i.KF_UP2))
<BR><BR>[Stack]<UL><LI>Max Depth = 2496<LI>Call Chain = KF_UP2 &rArr; ODS_Angle_Estimation &rArr; qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;symmetry
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat3_Inv
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat2_Inv
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetFunsionStatusAndMeasureUpdate
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ObsCalAndObsNoiseSet
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NavStandardParm2Flash
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CheckStandardCompleteStatus
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KfFeedback
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetNavStandardFlag
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NavKalmanMode
</UL>

<P><STRONG><a name="[13a]"></a>LEDIndicator</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, main.o(i.LEDIndicator))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LEDIndicator
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[101]"></a>Load_Calib_Parms</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, nav_imu.o(i.Load_Calib_Parms))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Load_Calib_Parms
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
</UL>

<P><STRONG><a name="[13d]"></a>Load_Standard_Data</STRONG> (Thumb, 416 bytes, Stack size 24 bytes, nav_imu.o(i.Load_Standard_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Load_Standard_Data &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetNavStandardFlag
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
</UL>

<P><STRONG><a name="[13e]"></a>MagInitHeading</STRONG> (Thumb, 460 bytes, Stack size 96 bytes, nav_magnet.o(i.MagInitHeading))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = MagInitHeading &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[133]"></a>Mat2_Inv</STRONG> (Thumb, 300 bytes, Stack size 48 bytes, nav_math.o(i.Mat2_Inv))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Mat2_Inv &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
</UL>

<P><STRONG><a name="[132]"></a>Mat3_Inv</STRONG> (Thumb, 1468 bytes, Stack size 88 bytes, nav_math.o(i.Mat3_Inv))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = Mat3_Inv &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
</UL>

<P><STRONG><a name="[139]"></a>Mat_Tr</STRONG> (Thumb, 52 bytes, Stack size 12 bytes, nav_math.o(i.Mat_Tr))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Mat_Tr
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WheelObsCalAndNoiseSet
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KfFeedback
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[5]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[141]"></a>NAV_Output</STRONG> (Thumb, 1984 bytes, Stack size 40 bytes, ins_output.o(i.NAV_Output))
<BR><BR>[Stack]<UL><LI>Max Depth = 656<LI>Call Chain = NAV_Output &rArr; protocol_send &rArr; frame_miscel_send &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_send
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_currentFreq
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_opticalgyro
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcusendtopcdriversdata
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[146]"></a>NAV_function</STRONG> (Thumb, 356 bytes, Stack size 8 bytes, nav_app.o(i.NAV_function))
<BR><BR>[Stack]<UL><LI>Max Depth = 2512<LI>Call Chain = NAV_function &rArr; NavKalmanMode &rArr; KF_UP2 &rArr; ODS_Angle_Estimation &rArr; qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Standard_Data
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_Init
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Param_Data
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_ODS_Data
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_GNSS_Data
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Param_Data_Init
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NavKalmanMode
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetNavStatus
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Out_Data_Up
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14b]"></a>NavKalmanMode</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, nav_app.o(i.NavKalmanMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 2504<LI>Call Chain = NavKalmanMode &rArr; KF_UP2 &rArr; ODS_Angle_Estimation &rArr; qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
</UL>

<P><STRONG><a name="[137]"></a>NavStandardParm2Flash</STRONG> (Thumb, 460 bytes, Stack size 8 bytes, nav_kf.o(i.NavStandardParm2Flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = NavStandardParm2Flash &rArr; comm_saveCaliData &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_saveCaliData
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
</UL>

<P><STRONG><a name="[12e]"></a>ODS_Angle_Estimation</STRONG> (Thumb, 3658 bytes, Stack size 1904 bytes, nav_ods.o(i.ODS_Angle_Estimation))
<BR><BR>[Stack]<UL><LI>Max Depth = 2304<LI>Call Chain = ODS_Angle_Estimation &rArr; qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;symmetry
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qdelphi
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;askew
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat3_Inv
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading_PI
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scalar_KalmanFilte
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
</UL>

<P><STRONG><a name="[130]"></a>ObsCalAndObsNoiseSet</STRONG> (Thumb, 500 bytes, Stack size 40 bytes, nav_kf.o(i.ObsCalAndObsNoiseSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 1016<LI>Call Chain = ObsCalAndObsNoiseSet &rArr; GPSObsCalAndNoiseSet &rArr; LeverarmTimeCorr2 &rArr; __hardfp_tan &rArr; __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WheelObsCalAndNoiseSet
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPSObsCalAndNoiseSet
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
</UL>

<P><STRONG><a name="[14c]"></a>Out_Data_Up</STRONG> (Thumb, 858 bytes, Stack size 8 bytes, nav.o(i.Out_Data_Up))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Out_Data_Up &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
</UL>

<P><STRONG><a name="[14f]"></a>ParaUpdateHandle</STRONG> (Thumb, 228 bytes, Stack size 32 bytes, setparabao.o(i.ParaUpdateHandle))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = ParaUpdateHandle &rArr; Drv_FlashWrite &rArr; Drv_FlashErase &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashWrite
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
</UL>

<P><STRONG><a name="[148]"></a>Param_Data_Init</STRONG> (Thumb, 146 bytes, Stack size 0 bytes, nav_app.o(i.Param_Data_Init))
<BR><BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
</UL>

<P><STRONG><a name="[150]"></a>ParseStrCmd</STRONG> (Thumb, 332 bytes, Stack size 8 bytes, nav_cli.o(i.ParseStrCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 2328<LI>Call Chain = ParseStrCmd &rArr; CliWriteConfig &rArr; CliReadConfig &rArr; Arm_SendMsg &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NavStandardParm2Flash
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliWriteConfig
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowNavStatus
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowNavSet
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowHelp
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowErrorCmd
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowBuildVersion
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowAbormal
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliSetStopNav
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliSetRestartNav
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliReadConfig
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
</UL>

<P><STRONG><a name="[108]"></a>Qnb2Cnb</STRONG> (Thumb, 844 bytes, Stack size 112 bytes, nav_math.o(i.Qnb2Cnb))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = Qnb2Cnb &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KfFeedback
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_Init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[154]"></a>Query378Interrupt</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.Query378Interrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Query378Interrupt
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>

<P><STRONG><a name="[156]"></a>ReadPara</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, setparabao.o(i.ReadPara))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = ReadPara &rArr; ReadPara_4 &rArr; ReadPara4_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_4
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_3
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_2
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_1
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_0
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[15c]"></a>ReadPara0_SetEnd</STRONG> (Thumb, 56 bytes, Stack size 80 bytes, setparabao.o(i.ReadPara0_SetEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = ReadPara0_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_0
</UL>

<P><STRONG><a name="[164]"></a>ReadPara0_SetHead</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, setparabao.o(i.ReadPara0_SetHead))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_0
</UL>

<P><STRONG><a name="[15e]"></a>ReadPara1_SetEnd</STRONG> (Thumb, 56 bytes, Stack size 80 bytes, setparabao.o(i.ReadPara1_SetEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = ReadPara1_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_1
</UL>

<P><STRONG><a name="[166]"></a>ReadPara1_SetHead</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, setparabao.o(i.ReadPara1_SetHead))
<BR><BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_1
</UL>

<P><STRONG><a name="[15f]"></a>ReadPara2_SetEnd</STRONG> (Thumb, 56 bytes, Stack size 80 bytes, setparabao.o(i.ReadPara2_SetEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = ReadPara2_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_2
</UL>

<P><STRONG><a name="[167]"></a>ReadPara2_SetHead</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, setparabao.o(i.ReadPara2_SetHead))
<BR><BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_2
</UL>

<P><STRONG><a name="[160]"></a>ReadPara3_SetEnd</STRONG> (Thumb, 56 bytes, Stack size 80 bytes, setparabao.o(i.ReadPara3_SetEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = ReadPara3_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_3
</UL>

<P><STRONG><a name="[168]"></a>ReadPara3_SetHead</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, setparabao.o(i.ReadPara3_SetHead))
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_3
</UL>

<P><STRONG><a name="[161]"></a>ReadPara4_SetEnd</STRONG> (Thumb, 56 bytes, Stack size 80 bytes, setparabao.o(i.ReadPara4_SetEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = ReadPara4_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_4
</UL>

<P><STRONG><a name="[169]"></a>ReadPara4_SetHead</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, setparabao.o(i.ReadPara4_SetHead))
<BR><BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_4
</UL>

<P><STRONG><a name="[162]"></a>ReadParaFromFlash</STRONG> (Thumb, 124 bytes, Stack size 592 bytes, setparabao.o(i.ReadParaFromFlash))
<BR><BR>[Stack]<UL><LI>Max Depth = 728<LI>Call Chain = ReadParaFromFlash &rArr; Drv_FlashWrite &rArr; Drv_FlashErase &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashWrite
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashRead_8bit
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitParaToAlgorithm
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[157]"></a>ReadPara_0</STRONG> (Thumb, 144 bytes, Stack size 400 bytes, setparabao.o(i.ReadPara_0))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = ReadPara_0 &rArr; ReadPara0_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara0_SetHead
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara0_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara
</UL>

<P><STRONG><a name="[158]"></a>ReadPara_1</STRONG> (Thumb, 146 bytes, Stack size 400 bytes, setparabao.o(i.ReadPara_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = ReadPara_1 &rArr; ReadPara1_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara1_SetHead
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara1_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara
</UL>

<P><STRONG><a name="[159]"></a>ReadPara_2</STRONG> (Thumb, 144 bytes, Stack size 400 bytes, setparabao.o(i.ReadPara_2))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = ReadPara_2 &rArr; ReadPara2_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara2_SetHead
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara2_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara
</UL>

<P><STRONG><a name="[15a]"></a>ReadPara_3</STRONG> (Thumb, 176 bytes, Stack size 400 bytes, setparabao.o(i.ReadPara_3))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = ReadPara_3 &rArr; ReadPara3_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara3_SetHead
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara3_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara
</UL>

<P><STRONG><a name="[15b]"></a>ReadPara_4</STRONG> (Thumb, 188 bytes, Stack size 400 bytes, setparabao.o(i.ReadPara_4))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = ReadPara_4 &rArr; ReadPara4_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara4_SetHead
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara4_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara
</UL>

<P><STRONG><a name="[16a]"></a>RestoreFactory</STRONG> (Thumb, 216 bytes, Stack size 992 bytes, setparabao.o(i.RestoreFactory))
<BR><BR>[Stack]<UL><LI>Max Depth = 1128<LI>Call Chain = RestoreFactory &rArr; Drv_FlashWrite &rArr; Drv_FlashErase &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashWrite
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[149]"></a>SINS_Init</STRONG> (Thumb, 530 bytes, Stack size 64 bytes, nav_sins.o(i.SINS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 496<LI>Call Chain = SINS_Init &rArr; Earth_UP &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
</UL>

<P><STRONG><a name="[14a]"></a>SINS_Update</STRONG> (Thumb, 3896 bytes, Stack size 624 bytes, nav_sins.o(i.SINS_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 1088<LI>Call Chain = SINS_Update &rArr; UpdateQnb &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnbmul
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cross3
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NavKalmanMode
</UL>

<P><STRONG><a name="[170]"></a>SPI_Exchange</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.SPI_Exchange))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
</UL>

<P><STRONG><a name="[174]"></a>SaveParaToFlash</STRONG> (Thumb, 158 bytes, Stack size 992 bytes, setparabao.o(i.SaveParaToFlash))
<BR><BR>[Stack]<UL><LI>Max Depth = 1128<LI>Call Chain = SaveParaToFlash &rArr; Drv_FlashWrite &rArr; Drv_FlashErase &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashWrite
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[14d]"></a>Scalar_KalmanFilte</STRONG> (Thumb, 354 bytes, Stack size 96 bytes, nav_ods.o(i.Scalar_KalmanFilte))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = Scalar_KalmanFilte &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
</UL>

<P><STRONG><a name="[16c]"></a>SendPara_SetEnd</STRONG> (Thumb, 56 bytes, Stack size 80 bytes, setparabao.o(i.SendPara_SetEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaVector
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaTime
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanR
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanQ
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGyroType
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGpsType
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnssInitValue
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnss
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFrequency
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFilter
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorGyro
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorAcc
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDeviation
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDebugMode
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDataOutType
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCoord
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCalibration
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAngle
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAll
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParaToFlash
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RestoreFactory
</UL>

<P><STRONG><a name="[16b]"></a>SendPara_SetHead</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, setparabao.o(i.SendPara_SetHead))
<BR><BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaVector
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaTime
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanR
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanQ
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGyroType
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGpsType
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnssInitValue
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnss
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFrequency
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFilter
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorGyro
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorAcc
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDeviation
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDebugMode
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDataOutType
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCoord
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCalibration
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAngle
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAll
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParaToFlash
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RestoreFactory
</UL>

<P><STRONG><a name="[127]"></a>SetCoordToAlgorithm</STRONG> (Thumb, 136 bytes, Stack size 0 bytes, setparabao.o(i.SetCoordToAlgorithm))
<BR><BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCoord
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAll
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitParaToAlgorithm
</UL>

<P><STRONG><a name="[12f]"></a>SetFunsionStatusAndMeasureUpdate</STRONG> (Thumb, 410 bytes, Stack size 0 bytes, nav_kf.o(i.SetFunsionStatusAndMeasureUpdate))
<BR><BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
</UL>

<P><STRONG><a name="[fa]"></a>SetGnssKalmanRmatrix</STRONG> (Thumb, 416 bytes, Stack size 16 bytes, nav_kf.o(i.SetGnssKalmanRmatrix))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = SetGnssKalmanRmatrix &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPSObsCalAndNoiseSet
</UL>

<P><STRONG><a name="[fd]"></a>SetNavRtkStatus</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, nav.o(i.SetNavRtkStatus))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_GNSS_Data
</UL>

<P><STRONG><a name="[136]"></a>SetNavStandardFlag</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, nav.o(i.SetNavStandardFlag))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Standard_Data
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
</UL>

<P><STRONG><a name="[103]"></a>SetNavStatus</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nav.o(i.SetNavStatus))
<BR><BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
</UL>

<P><STRONG><a name="[175]"></a>SetParaAll</STRONG> (Thumb, 524 bytes, Stack size 400 bytes, setparabao.o(i.SetParaAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaAll &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetCoordToAlgorithm
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_param_setbits
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_nav_para_syn
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[176]"></a>SetParaAngle</STRONG> (Thumb, 166 bytes, Stack size 400 bytes, setparabao.o(i.SetParaAngle))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaAngle &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_param_setbits
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_nav_para_syn
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[177]"></a>SetParaBaud</STRONG> (Thumb, 162 bytes, Stack size 400 bytes, setparabao.o(i.SetParaBaud))
<BR><BR>[Stack]<UL><LI>Max Depth = 532<LI>Call Chain = SetParaBaud &rArr; gd_eval_com_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[178]"></a>SetParaCalibration</STRONG> (Thumb, 136 bytes, Stack size 400 bytes, setparabao.o(i.SetParaCalibration))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaCalibration &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[179]"></a>SetParaCoord</STRONG> (Thumb, 128 bytes, Stack size 400 bytes, setparabao.o(i.SetParaCoord))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaCoord &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetCoordToAlgorithm
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[17a]"></a>SetParaDataOutType</STRONG> (Thumb, 138 bytes, Stack size 400 bytes, setparabao.o(i.SetParaDataOutType))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaDataOutType &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[17b]"></a>SetParaDebugMode</STRONG> (Thumb, 128 bytes, Stack size 400 bytes, setparabao.o(i.SetParaDebugMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaDebugMode &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[17c]"></a>SetParaDeviation</STRONG> (Thumb, 166 bytes, Stack size 400 bytes, setparabao.o(i.SetParaDeviation))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaDeviation &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_param_setbits
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_nav_para_syn
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[17d]"></a>SetParaFactorAcc</STRONG> (Thumb, 160 bytes, Stack size 400 bytes, setparabao.o(i.SetParaFactorAcc))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaFactorAcc &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[17e]"></a>SetParaFactorGyro</STRONG> (Thumb, 160 bytes, Stack size 400 bytes, setparabao.o(i.SetParaFactorGyro))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaFactorGyro &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[17f]"></a>SetParaFilter</STRONG> (Thumb, 122 bytes, Stack size 400 bytes, setparabao.o(i.SetParaFilter))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaFilter &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[180]"></a>SetParaFrequency</STRONG> (Thumb, 128 bytes, Stack size 400 bytes, setparabao.o(i.SetParaFrequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaFrequency &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[181]"></a>SetParaGnss</STRONG> (Thumb, 166 bytes, Stack size 400 bytes, setparabao.o(i.SetParaGnss))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaGnss &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_param_setbits
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_nav_para_syn
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[182]"></a>SetParaGnssInitValue</STRONG> (Thumb, 154 bytes, Stack size 400 bytes, setparabao.o(i.SetParaGnssInitValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaGnssInitValue &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[183]"></a>SetParaGpsType</STRONG> (Thumb, 138 bytes, Stack size 400 bytes, setparabao.o(i.SetParaGpsType))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaGpsType &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[184]"></a>SetParaGyroType</STRONG> (Thumb, 138 bytes, Stack size 400 bytes, setparabao.o(i.SetParaGyroType))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaGyroType &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[185]"></a>SetParaKalmanQ</STRONG> (Thumb, 122 bytes, Stack size 400 bytes, setparabao.o(i.SetParaKalmanQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaKalmanQ &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[186]"></a>SetParaKalmanR</STRONG> (Thumb, 122 bytes, Stack size 400 bytes, setparabao.o(i.SetParaKalmanR))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaKalmanR &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[187]"></a>SetParaTime</STRONG> (Thumb, 130 bytes, Stack size 400 bytes, setparabao.o(i.SetParaTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaTime &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[188]"></a>SetParaUpdateEnd</STRONG> (Thumb, 174 bytes, Stack size 400 bytes, setparabao.o(i.SetParaUpdateEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaUpdateEnd &rArr; UpdateEnd_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_SystemReset
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateEnd_SetHead
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateEnd_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[18c]"></a>SetParaUpdateSend</STRONG> (Thumb, 224 bytes, Stack size 400 bytes, setparabao.o(i.SetParaUpdateSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = SetParaUpdateSend &rArr; ParaUpdateHandle &rArr; Drv_FlashWrite &rArr; Drv_FlashErase &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateSend_SetHead
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateSend_SetEnd
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParaUpdateHandle
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[18f]"></a>SetParaUpdateStart</STRONG> (Thumb, 176 bytes, Stack size 400 bytes, setparabao.o(i.SetParaUpdateStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaUpdateStart &rArr; UpdateStart_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateStart_SetHead
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateStart_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[192]"></a>SetParaUpdateStop</STRONG> (Thumb, 116 bytes, Stack size 400 bytes, setparabao.o(i.SetParaUpdateStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaUpdateStop &rArr; UpdateStop_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateStop_SetHead
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateStop_SetEnd
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[195]"></a>SetParaVector</STRONG> (Thumb, 166 bytes, Stack size 400 bytes, setparabao.o(i.SetParaVector))
<BR><BR>[Stack]<UL><LI>Max Depth = 488<LI>Call Chain = SetParaVector &rArr; SendPara_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetHead
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_param_setbits
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_nav_para_syn
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
</UL>

<P><STRONG><a name="[196]"></a>Soft_I2C_Ack</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Soft_I2C_Ack &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
</UL>

<P><STRONG><a name="[198]"></a>Soft_I2C_Master_Init</STRONG> (Thumb, 260 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Soft_I2C_Master_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_interface_selection
</UL>

<P><STRONG><a name="[199]"></a>Soft_I2C_NAck</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_NAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Soft_I2C_NAck &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
</UL>

<P><STRONG><a name="[19a]"></a>Soft_I2C_Read_Byte</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = Soft_I2C_Read_Byte &rArr; Soft_I2C_NAck &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Input
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_NAck
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Ack
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>

<P><STRONG><a name="[19b]"></a>Soft_I2C_SDA_Input</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Input))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Soft_I2C_SDA_Input &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
</UL>

<P><STRONG><a name="[197]"></a>Soft_I2C_SDA_Output</STRONG> (Thumb, 196 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_NAck
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Ack
</UL>

<P><STRONG><a name="[19c]"></a>Soft_I2C_Send_Byte</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Soft_I2C_Send_Byte &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>

<P><STRONG><a name="[19d]"></a>Soft_I2C_Start</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Soft_I2C_Start &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>

<P><STRONG><a name="[19e]"></a>Soft_I2C_Stop</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Soft_I2C_Stop &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>

<P><STRONG><a name="[19f]"></a>Soft_I2C_Wait_Ack</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Soft_I2C_Wait_Ack &rArr; Soft_I2C_Stop &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Input
</UL>
<BR>[Called By]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>

<P><STRONG><a name="[1a0]"></a>Spi395Exchange</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, ch395spi.o(i.Spi395Exchange))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Spi395Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Data
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH395Data
</UL>

<P><STRONG><a name="[16d]"></a>StartCoarseAlign</STRONG> (Thumb, 1512 bytes, Stack size 72 bytes, nav_sins.o(i.StartCoarseAlign))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = StartCoarseAlign &rArr; att2qnb &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
</UL>

<P><STRONG><a name="[b]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>SystemInit</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[e]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, bsp_rtc.o(i.TAMPER_STAMP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = TAMPER_STAMP_IRQHandler &rArr; rtc_show_timestamp &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_flag_get
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_flag_clear
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[11b]"></a>TCPServer_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, tcpserver.o(i.TCPServer_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = TCPServer_Init &rArr; CH395Init &rArr; CH395CMDInitCH395 &rArr; CH395GetCmdStatus &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_RST
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_PORT_INIT
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetTCPMss
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket_buffer_config
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitCH395InfParam
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395Init
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[25]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 944<LI>Call Chain = TIMER0_UP_TIMER9_IRQHandler &rArr; generateCSVLogFileName &rArr; sow2Date &rArr; gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.TIMER1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 156 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIMER2_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>UART4_IRQHandler</STRONG> (Thumb, 204 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.UART4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART4_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_disable
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>UART6_IRQHandler</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.UART6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART6_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_disable
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1af]"></a>UartDataHandle</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, firmwareupdatefile.o(i.UartDataHandle))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = UartDataHandle &rArr; Uart_UpdateFirm_nav &rArr; UpdateFileHandle &rArr; Drv_FlashWrite &rArr; Drv_FlashErase &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_UpdateFirm_nav
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_SystemReset
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1b1]"></a>UartDmaRecSetPara</STRONG> (Thumb, 512 bytes, Stack size 16 bytes, setparabao.o(i.UartDmaRecSetPara))
<BR><BR>[Stack]<UL><LI>Max Depth = 1144<LI>Call Chain = UartDmaRecSetPara &rArr; SaveParaToFlash &rArr; Drv_FlashWrite &rArr; Drv_FlashErase &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaVector
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStop
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStart
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateEnd
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaTime
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanR
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanQ
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGyroType
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGpsType
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnssInitValue
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnss
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFrequency
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFilter
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorGyro
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorAcc
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDeviation
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDebugMode
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDataOutType
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCoord
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCalibration
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAngle
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAll
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParaToFlash
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RestoreFactory
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara
</UL>
<BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
</UL>

<P><STRONG><a name="[1b2]"></a>UartSend_UpdateFirm</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, firmwareupdatefile.o(i.UartSend_UpdateFirm))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = UartSend_UpdateFirm &rArr; UartSend_combag_setheadend_senddo_uart6 &rArr; UartSend_nav2up_SetHead &rArr; ushort2Buf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_combag_setheadend_senddo_uart6
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_UpdateFirm_nav
</UL>

<P><STRONG><a name="[1b3]"></a>UartSend_combag_setheadend_senddo_uart6</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, firmwareupdatefile.o(i.UartSend_combag_setheadend_senddo_uart6))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = UartSend_combag_setheadend_senddo_uart6 &rArr; UartSend_nav2up_SetHead &rArr; ushort2Buf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateFirmsendmsg
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_nav2up_SetHead
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_nav2up_SetEnd
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_UpdateFirm
</UL>

<P><STRONG><a name="[1b5]"></a>UartSend_nav2up_SetEnd</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, firmwareupdatefile.o(i.UartSend_nav2up_SetEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = UartSend_nav2up_SetEnd &rArr; ushort2Buf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ushort2Buf
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_combag_setheadend_senddo_uart6
</UL>

<P><STRONG><a name="[1b4]"></a>UartSend_nav2up_SetHead</STRONG> (Thumb, 64 bytes, Stack size 20 bytes, firmwareupdatefile.o(i.UartSend_nav2up_SetHead))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UartSend_nav2up_SetHead &rArr; ushort2Buf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ushort2Buf
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_combag_setheadend_senddo_uart6
</UL>

<P><STRONG><a name="[116]"></a>Uart_RxInit</STRONG> (Thumb, 250 bytes, Stack size 24 bytes, bsp_fmc.o(i.Uart_RxInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Uart_RxInit
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[9d]"></a>Uart_SendMsg</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, bsp_fmc.o(i.Uart_SendMsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_send_end_frame
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Arm_SendMsg
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
</UL>

<P><STRONG><a name="[115]"></a>Uart_TxInit</STRONG> (Thumb, 238 bytes, Stack size 24 bytes, bsp_fmc.o(i.Uart_TxInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Uart_TxInit
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[1b0]"></a>Uart_UpdateFirm_nav</STRONG> (Thumb, 226 bytes, Stack size 128 bytes, firmwareupdatefile.o(i.Uart_UpdateFirm_nav))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = Uart_UpdateFirm_nav &rArr; UpdateFileHandle &rArr; Drv_FlashWrite &rArr; Drv_FlashErase &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateFileHandle
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_UpdateFirm
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_SystemReset
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CheckSum
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDataHandle
</UL>

<P><STRONG><a name="[18a]"></a>UpdateEnd_SetEnd</STRONG> (Thumb, 56 bytes, Stack size 80 bytes, setparabao.o(i.UpdateEnd_SetEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UpdateEnd_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateEnd
</UL>

<P><STRONG><a name="[189]"></a>UpdateEnd_SetHead</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, setparabao.o(i.UpdateEnd_SetHead))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateEnd
</UL>

<P><STRONG><a name="[1b9]"></a>UpdateFileHandle</STRONG> (Thumb, 210 bytes, Stack size 32 bytes, firmwareupdatefile.o(i.UpdateFileHandle))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = UpdateFileHandle &rArr; Drv_FlashWrite &rArr; Drv_FlashErase &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashWrite
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_UpdateFirm_nav
</UL>

<P><STRONG><a name="[1b6]"></a>UpdateFirmsendmsg</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, gd32f4xx_it.o(i.UpdateFirmsendmsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UpdateFirmsendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_combag_setheadend_senddo_uart6
</UL>

<P><STRONG><a name="[16f]"></a>UpdateQnb</STRONG> (Thumb, 946 bytes, Stack size 224 bytes, nav_math.o(i.UpdateQnb))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = UpdateQnb &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnbmul
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
</UL>

<P><STRONG><a name="[18e]"></a>UpdateSend_SetEnd</STRONG> (Thumb, 56 bytes, Stack size 80 bytes, setparabao.o(i.UpdateSend_SetEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UpdateSend_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
</UL>

<P><STRONG><a name="[18d]"></a>UpdateSend_SetHead</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, setparabao.o(i.UpdateSend_SetHead))
<BR><BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
</UL>

<P><STRONG><a name="[191]"></a>UpdateStart_SetEnd</STRONG> (Thumb, 56 bytes, Stack size 80 bytes, setparabao.o(i.UpdateStart_SetEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UpdateStart_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStart
</UL>

<P><STRONG><a name="[190]"></a>UpdateStart_SetHead</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, setparabao.o(i.UpdateStart_SetHead))
<BR><BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStart
</UL>

<P><STRONG><a name="[194]"></a>UpdateStop_SetEnd</STRONG> (Thumb, 56 bytes, Stack size 80 bytes, setparabao.o(i.UpdateStop_SetEnd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UpdateStop_SetEnd &rArr; crc_verify_8bit
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_verify_8bit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStop
</UL>

<P><STRONG><a name="[193]"></a>UpdateStop_SetHead</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, setparabao.o(i.UpdateStop_SetHead))
<BR><BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStop
</UL>

<P><STRONG><a name="[131]"></a>Update_Phi_HP</STRONG> (Thumb, 1956 bytes, Stack size 472 bytes, nav_kf.o(i.Update_Phi_HP))
<BR><BR>[Stack]<UL><LI>Max Depth = 744<LI>Call Chain = Update_Phi_HP &rArr; __hardfp_tan &rArr; __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;askew
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EyeMatrix
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
</UL>

<P><STRONG><a name="[7]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a5]"></a>Wait378Interrupt</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, file_sys.o(i.Wait378Interrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Query378Interrupt
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
</UL>

<P><STRONG><a name="[14e]"></a>WheelObsCalAndNoiseSet</STRONG> (Thumb, 770 bytes, Stack size 352 bytes, nav_kf.o(i.WheelObsCalAndNoiseSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 560<LI>Call Chain = WheelObsCalAndNoiseSet &rArr; matmul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;askew
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ObsCalAndObsNoiseSet
</UL>

<P><STRONG><a name="[10c]"></a>WheelSpeedOptimize</STRONG> (Thumb, 106 bytes, Stack size 64 bytes, nav_ods.o(i.WheelSpeedOptimize))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = WheelSpeedOptimize &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_ODS_Data
</UL>

<P><STRONG><a name="[1bb]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[267]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[147]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[268]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[269]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1bd]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[26a]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[d5]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowNavStatus
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowHelp
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliShowErrorCmd
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliReadConfig
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
</UL>

<P><STRONG><a name="[26b]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[26c]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[1c2]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
</UL>

<P><STRONG><a name="[1ca]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[7d]"></a>__aeabi_errno_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[26d]"></a>__rt_errno_addr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr), UNUSED)

<P><STRONG><a name="[1be]"></a>__hardfp_asin</STRONG> (Thumb, 770 bytes, Stack size 88 bytes, asin.o(i.__hardfp_asin))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __hardfp_asin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
</UL>

<P><STRONG><a name="[1c7]"></a>__hardfp_atan</STRONG> (Thumb, 622 bytes, Stack size 48 bytes, atan.o(i.__hardfp_atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[104]"></a>__hardfp_atan2</STRONG> (Thumb, 432 bytes, Stack size 48 bytes, atan2.o(i.__hardfp_atan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[13f]"></a>__hardfp_atan2f</STRONG> (Thumb, 502 bytes, Stack size 16 bytes, atan2f.o(i.__hardfp_atan2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan2
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
</UL>

<P><STRONG><a name="[e3]"></a>__hardfp_atof</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, atof.o(i.__hardfp_atof))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = __hardfp_atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliWriteConfig
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_split_fnum
</UL>

<P><STRONG><a name="[f5]"></a>__hardfp_cos</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, cos.o(i.__hardfp_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_Output
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ObsCalAndObsNoiseSet
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rv2q
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
</UL>

<P><STRONG><a name="[fe]"></a>__hardfp_exp</STRONG> (Thumb, 714 bytes, Stack size 72 bytes, exp.o(i.__hardfp_exp))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = __hardfp_exp &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_GNSS_Data
</UL>

<P><STRONG><a name="[9a]"></a>__hardfp_fabs</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fabs.o(i.__hardfp_fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Standard_Data
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_ODS_Data
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gyro_data_check
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Acc_data_check
</UL>

<P><STRONG><a name="[1d2]"></a>__hardfp_floor</STRONG> (Thumb, 252 bytes, Stack size 40 bytes, floor.o(i.__hardfp_floor))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>

<P><STRONG><a name="[ff]"></a>__hardfp_pow</STRONG> (Thumb, 3072 bytes, Stack size 192 bytes, pow.o(i.__hardfp_pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
</UL>
<BR>[Called By]<UL><LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_GNSS_Data
</UL>

<P><STRONG><a name="[f4]"></a>__hardfp_sin</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, sin.o(i.__hardfp_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;att2qnb
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ObsCalAndObsNoiseSet
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MagInitHeading
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rv2q
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
</UL>

<P><STRONG><a name="[d1]"></a>__hardfp_sqrt</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, sqrt.o(i.__hardfp_sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __hardfp_sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CheckStandardCompleteStatus
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_ODS_Data
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
</UL>

<P><STRONG><a name="[13c]"></a>__hardfp_tan</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, tan.o(i.__hardfp_tan))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = __hardfp_tan &rArr; __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ObsCalAndObsNoiseSet
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
</UL>

<P><STRONG><a name="[1ce]"></a>__ieee754_rem_pio2</STRONG> (Thumb, 938 bytes, Stack size 120 bytes, rred.o(i.__ieee754_rem_pio2))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[1d0]"></a>__kernel_cos</STRONG> (Thumb, 322 bytes, Stack size 64 bytes, cos_i.o(i.__kernel_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = __kernel_cos &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[1c4]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[1cf]"></a>__kernel_sin</STRONG> (Thumb, 280 bytes, Stack size 72 bytes, sin_i.o(i.__kernel_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = __kernel_sin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>

<P><STRONG><a name="[1d5]"></a>__kernel_tan</STRONG> (Thumb, 764 bytes, Stack size 128 bytes, tan_i.o(i.__kernel_tan))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
</UL>

<P><STRONG><a name="[1d3]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_divzero))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_divzero &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1bf]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[1c8]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1c1]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[1d1]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_overflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[1c3]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
</UL>

<P><STRONG><a name="[1cc]"></a>__mathlib_flt_infnan2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan2))
<BR><BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[1cb]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[1cd]"></a>__read_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__read_errno))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
</UL>

<P><STRONG><a name="[26e]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[26f]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[270]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[1c0]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[7a]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scanf_fp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[98]"></a>adc_interrupt_flag_clear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>

<P><STRONG><a name="[1da]"></a>adj_paraSyn</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, computerframeparse.o(i.adj_paraSyn))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = adj_paraSyn
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
</UL>

<P><STRONG><a name="[1db]"></a>analysisRxdata</STRONG> (Thumb, 1622 bytes, Stack size 104 bytes, protocol.o(i.analysisRxdata))
<BR><BR>[Stack]<UL><LI>Max Depth = 2432<LI>Call Chain = analysisRxdata &rArr; ParseStrCmd &rArr; CliWriteConfig &rArr; CliReadConfig &rArr; Arm_SendMsg &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcusendtopcdriversdata
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDmaRecSetPara
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13b]"></a>askew</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, nav_math.o(i.askew))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = askew
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WheelObsCalAndNoiseSet
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
</UL>

<P><STRONG><a name="[1c9]"></a>atan</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, atan.o(i.atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
</UL>

<P><STRONG><a name="[105]"></a>att2qnb</STRONG> (Thumb, 694 bytes, Stack size 112 bytes, nav_math.o(i.att2qnb))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = att2qnb &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_Init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;StartCoarseAlign
</UL>

<P><STRONG><a name="[11d]"></a>bmp280_init</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, bmp280.o(i.bmp280_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = bmp280_init &rArr; bmp2_set_power_mode &rArr; conf_sensor &rArr; bmp2_soft_reset &rArr; bmp2_set_regs &rArr; interleave_data
</UL>
<BR>[Calls]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_interface_selection
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_set_power_mode
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_set_config
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_init
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_config
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_compute_meas_time
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[1e1]"></a>bmp2_compute_meas_time</STRONG> (Thumb, 92 bytes, Stack size 72 bytes, bmp2.o(i.bmp2_compute_meas_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = bmp2_compute_meas_time
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;null_ptr_check
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp280_init
</UL>

<P><STRONG><a name="[6f]"></a>bmp2_delay_us</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, common.o(i.bmp2_delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bmp2_delay_us &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Address Reference Count : 1]<UL><LI> common.o(i.bmp2_interface_selection)
</UL>
<P><STRONG><a name="[1de]"></a>bmp2_get_config</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, bmp2.o(i.bmp2_get_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = bmp2_get_config &rArr; bmp2_get_regs
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_regs
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp280_init
</UL>

<P><STRONG><a name="[1e3]"></a>bmp2_get_regs</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, bmp2.o(i.bmp2_get_regs))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = bmp2_get_regs
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;null_ptr_check
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_init
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_config
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_calib_param
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conf_sensor
</UL>

<P><STRONG><a name="[6d]"></a>bmp2_i2c_read</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, common.o(i.bmp2_i2c_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = bmp2_i2c_read &rArr; i2c_read &rArr; Soft_I2C_Read_Byte &rArr; Soft_I2C_NAck &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> common.o(i.bmp2_interface_selection)
</UL>
<P><STRONG><a name="[6e]"></a>bmp2_i2c_write</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, common.o(i.bmp2_i2c_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = bmp2_i2c_write &rArr; i2c_write &rArr; Soft_I2C_Wait_Ack &rArr; Soft_I2C_Stop &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> common.o(i.bmp2_interface_selection)
</UL>
<P><STRONG><a name="[1dd]"></a>bmp2_init</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, bmp2.o(i.bmp2_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = bmp2_init &rArr; get_calib_param &rArr; bmp2_get_regs
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_regs
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;null_ptr_check
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_calib_param
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp280_init
</UL>

<P><STRONG><a name="[1dc]"></a>bmp2_interface_selection</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, common.o(i.bmp2_interface_selection))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = bmp2_interface_selection &rArr; Soft_I2C_Master_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Master_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp280_init
</UL>

<P><STRONG><a name="[1df]"></a>bmp2_set_config</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, bmp2.o(i.bmp2_set_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = bmp2_set_config &rArr; conf_sensor &rArr; bmp2_soft_reset &rArr; bmp2_set_regs &rArr; interleave_data
</UL>
<BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conf_sensor
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp280_init
</UL>

<P><STRONG><a name="[1e0]"></a>bmp2_set_power_mode</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, bmp2.o(i.bmp2_set_power_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = bmp2_set_power_mode &rArr; conf_sensor &rArr; bmp2_soft_reset &rArr; bmp2_set_regs &rArr; interleave_data
</UL>
<BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conf_sensor
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp280_init
</UL>

<P><STRONG><a name="[1e8]"></a>bmp2_set_regs</STRONG> (Thumb, 144 bytes, Stack size 40 bytes, bmp2.o(i.bmp2_set_regs))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = bmp2_set_regs &rArr; interleave_data
</UL>
<BR>[Calls]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;null_ptr_check
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;interleave_data
</UL>
<BR>[Called By]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_soft_reset
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conf_sensor
</UL>

<P><STRONG><a name="[1ea]"></a>bmp2_soft_reset</STRONG> (Thumb, 30 bytes, Stack size 24 bytes, bmp2.o(i.bmp2_soft_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = bmp2_soft_reset &rArr; bmp2_set_regs &rArr; interleave_data
</UL>
<BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_set_regs
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conf_sensor
</UL>

<P><STRONG><a name="[119]"></a>bsp_can_init</STRONG> (Thumb, 222 bytes, Stack size 8 bytes, bsp_can.o(i.bsp_can_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bsp_can_init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_interrupt_enable
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_init
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_filter_init
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_deinit
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[1ef]"></a>bsp_exti_config</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, bsp_exti.o(i.bsp_exti_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bsp_exti_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;syscfg_exti_line_config
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_init
</UL>

<P><STRONG><a name="[11c]"></a>bsp_exti_init</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, bsp_exti.o(i.bsp_exti_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = bsp_exti_init &rArr; bsp_exti_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[111]"></a>bsp_gpio_init</STRONG> (Thumb, 446 bytes, Stack size 8 bytes, bsp_gpio.o(i.bsp_gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = bsp_gpio_init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[118]"></a>bsp_rtc_init</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, bsp_rtc.o(i.bsp_rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = bsp_rtc_init &rArr; rtc_setup &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_timestamp_enable
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_interrupt_enable
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_flag_clear
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_all_reset_flag_clear
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[112]"></a>bsp_tim_init</STRONG> (Thumb, 344 bytes, Stack size 32 bytes, bsp_tim.o(i.bsp_tim_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = bsp_tim_init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_timer_clock_prescaler_config
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_shadow_config
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_pulse_value_config
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_mode_config
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_config
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_auto_reload_shadow_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[250]"></a>bufExchangeHL2pos</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, firmwareupdatefile.o(i.bufExchangeHL2pos))
<BR><BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ushort2Buf
</UL>

<P><STRONG><a name="[204]"></a>calcGPRMC_TRA</STRONG> (Thumb, 210 bytes, Stack size 48 bytes, main.o(i.calcGPRMC_TRA))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = calcGPRMC_TRA &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[1eb]"></a>can_deinit</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, gd32f4xx_can.o(i.can_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = can_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
</UL>

<P><STRONG><a name="[1ed]"></a>can_filter_init</STRONG> (Thumb, 262 bytes, Stack size 8 bytes, gd32f4xx_can.o(i.can_filter_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = can_filter_init
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
</UL>

<P><STRONG><a name="[1ec]"></a>can_init</STRONG> (Thumb, 286 bytes, Stack size 16 bytes, gd32f4xx_can.o(i.can_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = can_init
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
</UL>

<P><STRONG><a name="[1ee]"></a>can_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_can.o(i.can_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
</UL>

<P><STRONG><a name="[9e]"></a>can_message_receive</STRONG> (Thumb, 228 bytes, Stack size 8 bytes, gd32f4xx_can.o(i.can_message_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = can_message_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
</UL>

<P><STRONG><a name="[21c]"></a>comm_axis_read</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_axis_read))
<BR><BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>

<P><STRONG><a name="[207]"></a>comm_fm_update</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, computerframeparse.o(i.comm_fm_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = comm_fm_update &rArr; strncpy
</UL>
<BR>[Calls]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
</UL>

<P><STRONG><a name="[128]"></a>comm_nav_para_syn</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, computerframeparse.o(i.comm_nav_para_syn))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = comm_nav_para_syn
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaVector
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnss
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDeviation
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAngle
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAll
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitParaToAlgorithm
</UL>

<P><STRONG><a name="[126]"></a>comm_param_setbits</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_param_setbits))
<BR><BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaVector
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnss
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDeviation
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAngle
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAll
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitParaToAlgorithm
</UL>

<P><STRONG><a name="[145]"></a>comm_read_currentFreq</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_read_currentFreq))
<BR><BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_form
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_Output
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
</UL>

<P><STRONG><a name="[209]"></a>comm_resume_defaultPara</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, computerframeparse.o(i.comm_resume_defaultPara))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = comm_resume_defaultPara &rArr; comm_set_customPara &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
</UL>

<P><STRONG><a name="[df]"></a>comm_saveCaliData</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, computerframeparse.o(i.comm_saveCaliData))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = comm_saveCaliData &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hash32
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NavStandardParm2Flash
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliWriteConfig
</UL>

<P><STRONG><a name="[123]"></a>comm_send_end_frame</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, computerframeparse.o(i.comm_send_end_frame))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = comm_send_end_frame &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[e2]"></a>comm_set_customPara</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, computerframeparse.o(i.comm_set_customPara))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = comm_set_customPara &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hash32
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliWriteConfig
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_resume_defaultPara
</UL>

<P><STRONG><a name="[122]"></a>comm_store_init</STRONG> (Thumb, 276 bytes, Stack size 24 bytes, computerframeparse.o(i.comm_store_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = comm_store_init &rArr; comm_resume_defaultPara &rArr; comm_set_customPara &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_TxInit
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_RxInit
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_read_8bit_data
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_saveCaliData
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hash32
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_resume_defaultPara
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_nav_para_syn
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_fm_update
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adj_paraSyn
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[15d]"></a>crc_verify_8bit</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, firmwareupdatefile.o(i.crc_verify_8bit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = crc_verify_8bit
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_nav2up_SetEnd
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateStop_SetEnd
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateStart_SetEnd
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateSend_SetEnd
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateEnd_SetEnd
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaVector
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStop
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStart
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateEnd
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaTime
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanR
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanQ
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGyroType
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGpsType
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnssInitValue
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnss
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFrequency
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFilter
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorGyro
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorAcc
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDeviation
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDebugMode
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDataOutType
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCoord
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCalibration
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAngle
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAll
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendPara_SetEnd
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParaToFlash
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RestoreFactory
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_4
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_3
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_2
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_1
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_0
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara4_SetEnd
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara3_SetEnd
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara2_SetEnd
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara1_SetEnd
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara0_SetEnd
</UL>

<P><STRONG><a name="[f7]"></a>cross3</STRONG> (Thumb, 294 bytes, Stack size 32 bytes, nav_math.o(i.cross3))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = cross3 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
</UL>

<P><STRONG><a name="[1a1]"></a>delay_decrement</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[10f]"></a>delay_init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, systick.o(i.delay_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_init
</UL>
<BR>[Calls]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_clksource_set
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[bb]"></a>delay_ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_ms))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelaymS
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDInitCH395
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_interface_selection
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelaymS
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStart
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateEnd
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
</UL>

<P><STRONG><a name="[bc]"></a>delay_us</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, systick.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_delay_us
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelayuS
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_NAck
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Ack
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
</UL>

<P><STRONG><a name="[210]"></a>epoch2time</STRONG> (Thumb, 354 bytes, Stack size 112 bytes, time_unify.o(i.epoch2time))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
</UL>

<P><STRONG><a name="[114]"></a>exmc_asynchronous_sram_init</STRONG> (Thumb, 364 bytes, Stack size 120 bytes, bsp_fmc.o(i.exmc_asynchronous_sram_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = exmc_asynchronous_sram_init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_init
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_enable
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[212]"></a>exmc_norsram_enable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(i.exmc_norsram_enable))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
</UL>

<P><STRONG><a name="[211]"></a>exmc_norsram_init</STRONG> (Thumb, 224 bytes, Stack size 12 bytes, gd32f4xx_exmc.o(i.exmc_norsram_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = exmc_norsram_init
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
</UL>

<P><STRONG><a name="[1f1]"></a>exti_init</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, gd32f4xx_exti.o(i.exti_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = exti_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
</UL>

<P><STRONG><a name="[ef]"></a>exti_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI3_IRQHandler
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
</UL>

<P><STRONG><a name="[ee]"></a>exti_interrupt_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI3_IRQHandler
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
</UL>

<P><STRONG><a name="[1c5]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[213]"></a>fmc_byte_program</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, gd32f4xx_fmc.o(i.fmc_byte_program))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fmc_byte_program &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
</UL>

<P><STRONG><a name="[20a]"></a>fmc_erase_sector_by_address</STRONG> (Thumb, 68 bytes, Stack size 48 bytes, bsp_fmc.o(i.fmc_erase_sector_by_address))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = fmc_erase_sector_by_address &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_flag_clear
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_info_get
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_saveCaliData
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
</UL>

<P><STRONG><a name="[e9]"></a>fmc_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashWrite
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
</UL>

<P><STRONG><a name="[eb]"></a>fmc_lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_lock))
<BR><BR>[Called By]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashWrite
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
</UL>

<P><STRONG><a name="[20d]"></a>fmc_read_8bit_data</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, bsp_fmc.o(i.fmc_read_8bit_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fmc_read_8bit_data
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
</UL>

<P><STRONG><a name="[214]"></a>fmc_ready_wait</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_fmc.o(i.fmc_ready_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_state_get
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_word_program
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_byte_program
</UL>

<P><STRONG><a name="[ea]"></a>fmc_sector_erase</STRONG> (Thumb, 90 bytes, Stack size 12 bytes, gd32f4xx_fmc.o(i.fmc_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = fmc_sector_erase &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
</UL>

<P><STRONG><a name="[e7]"></a>fmc_sector_info_get</STRONG> (Thumb, 366 bytes, Stack size 40 bytes, bsp_fmc.o(i.fmc_sector_info_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
</UL>

<P><STRONG><a name="[215]"></a>fmc_state_get</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>

<P><STRONG><a name="[e8]"></a>fmc_unlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_unlock))
<BR><BR>[Called By]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashWrite
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashErase
</UL>

<P><STRONG><a name="[ed]"></a>fmc_word_program</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, gd32f4xx_fmc.o(i.fmc_word_program))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fmc_word_program &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Drv_FlashWrite
</UL>

<P><STRONG><a name="[20c]"></a>fmc_write_8bit_data</STRONG> (Thumb, 132 bytes, Stack size 88 bytes, bsp_fmc.o(i.fmc_write_8bit_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_flag_clear
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_byte_program
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sector_name_to_number
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_info_get
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_saveCaliData
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
</UL>

<P><STRONG><a name="[6b]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, main.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[218]"></a>frame_form</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, frame_analysis.o(i.frame_form))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = frame_form &rArr; frame_miscel_send &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_currentFreq
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_imu_and_gnss_send
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
</UL>
<BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[21a]"></a>frame_imu_and_gnss_send</STRONG> (Thumb, 1218 bytes, Stack size 280 bytes, frame_analysis.o(i.frame_imu_and_gnss_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = frame_imu_and_gnss_send &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_currentFreq
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_axis_read
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_form
</UL>

<P><STRONG><a name="[21b]"></a>frame_miscel_send</STRONG> (Thumb, 2458 bytes, Stack size 560 bytes, frame_analysis.o(i.frame_miscel_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = frame_miscel_send &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_currentFreq
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_form
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_send
</UL>

<P><STRONG><a name="[219]"></a>frame_navi_and_gnss_send</STRONG> (Thumb, 430 bytes, Stack size 256 bytes, frame_analysis.o(i.frame_navi_and_gnss_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = frame_navi_and_gnss_send &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_currentFreq
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_update
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_form
</UL>

<P><STRONG><a name="[21e]"></a>frame_pack_and_send</STRONG> (Thumb, 2362 bytes, Stack size 88 bytes, frame_analysis.o(i.frame_pack_and_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = frame_pack_and_send &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_currentFreq
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xor_check
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_axis_read
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_send
</UL>

<P><STRONG><a name="[23e]"></a>free</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, malloc.o(i.free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = free
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qdelphi
</UL>

<P><STRONG><a name="[11f]"></a>gd_eval_com_init</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, main.o(i.gd_eval_com_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = gd_eval_com_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
</UL>

<P><STRONG><a name="[121]"></a>gd_eval_com_init6</STRONG> (Thumb, 208 bytes, Stack size 16 bytes, main.o(i.gd_eval_com_init6))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = gd_eval_com_init6 &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_word_length_set
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_stop_bit_set
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_parity_config
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_rts_config
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_cts_config
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_clear
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[1a8]"></a>generateCSVLogFileName</STRONG> (Thumb, 86 bytes, Stack size 544 bytes, logger.o(i.generateCSVLogFileName))
<BR><BR>[Stack]<UL><LI>Max Depth = 936<LI>Call Chain = generateCSVLogFileName &rArr; sow2Date &rArr; gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
</UL>

<P><STRONG><a name="[22f]"></a>get_16bit_D32</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, main.o(i.get_16bit_D32))
<BR><BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[230]"></a>get_16bit_D64</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, main.o(i.get_16bit_D64))
<BR><BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[22e]"></a>get_16bit_Int32</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, main.o(i.get_16bit_Int32))
<BR><BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[22d]"></a>get_fpgadata</STRONG> (Thumb, 2754 bytes, Stack size 1072 bytes, main.o(i.get_fpgadata))
<BR><BR>[Stack]<UL><LI>Max Depth = 1688<LI>Call Chain = get_fpgadata &rArr; frame_form &rArr; frame_miscel_send &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_form
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_16bit_Int32
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_16bit_D64
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_16bit_D32
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calcGPRMC_TRA
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b4]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_PORT_INIT
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
</UL>

<P><STRONG><a name="[a0]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDIndicator
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_RST
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Data
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395GetCmdStatus
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_NAck
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Ack
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetICVer
</UL>

<P><STRONG><a name="[a1]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDIndicator
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_RST
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_PORT_INIT
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetTCPMss
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetStartPara
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetSocketSendBuf
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetSocketRecvBuf
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetMASKAddr
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetIPAddr
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetGWIPAddr
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDInitCH395
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDCheckExist
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Master_Init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395GetCmdStatus
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_NAck
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Ack
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetICVer
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
</UL>

<P><STRONG><a name="[155]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Query378Interrupt
</UL>

<P><STRONG><a name="[b5]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_PORT_INIT
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Master_Init
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Input
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
</UL>

<P><STRONG><a name="[b6]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_PORT_INIT
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Master_Init
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
</UL>

<P><STRONG><a name="[231]"></a>gpst2time</STRONG> (Thumb, 142 bytes, Stack size 48 bytes, time_unify.o(i.gpst2time))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = gpst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[232]"></a>gpst2utc</STRONG> (Thumb, 164 bytes, Stack size 88 bytes, time_unify.o(i.gpst2utc))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = gpst2utc &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[235]"></a>gst2time</STRONG> (Thumb, 142 bytes, Stack size 48 bytes, time_unify.o(i.gst2time))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>
<BR>[Called By]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
</UL>

<P><STRONG><a name="[20b]"></a>hash32</STRONG> (Thumb, 60 bytes, Stack size 20 bytes, computerframeparse.o(i.hash32))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = hash32
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_saveCaliData
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
</UL>

<P><STRONG><a name="[1e4]"></a>i2c_read</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, common.o(i.i2c_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = i2c_read &rArr; Soft_I2C_Read_Byte &rArr; Soft_I2C_NAck &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_i2c_read
</UL>

<P><STRONG><a name="[1e5]"></a>i2c_write</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, common.o(i.i2c_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = i2c_write &rArr; Soft_I2C_Wait_Ack &rArr; Soft_I2C_Stop &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_i2c_write
</UL>

<P><STRONG><a name="[110]"></a>initializationdriversettings</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, gdwatch.o(i.initializationdriversettings))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = initializationdriversettings
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[fc]"></a>is_gnss_update</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, nav_gnss.o(i.is_gnss_update))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_GNSS_Data
</UL>

<P><STRONG><a name="[c2]"></a>mDelaymS</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ch395spi.o(i.mDelaymS))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = mDelaymS
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_RST
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDInitCH395
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395Init
</UL>

<P><STRONG><a name="[236]"></a>mDelayuS</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ch395spi.o(i.mDelayuS))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH395Cmd
</UL>

<P><STRONG><a name="[117]"></a>mInitCH378Host</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.mInitCH378Host))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = mInitCH378Host &rArr; CH378_Port_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetICVer
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[65]"></a>main</STRONG> (Thumb, 470 bytes, Stack size 32 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 2544<LI>Call Chain = main &rArr; NAV_function &rArr; NavKalmanMode &rArr; KF_UP2 &rArr; ODS_Angle_Estimation &rArr; qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_vector_table_set
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_all_reset_flag_clear
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wheel_is_running
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelaymS
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartDataHandle
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadParaFromFlash
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_function
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_Output
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDIndicator
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart6sendmsg
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[252]"></a>malloc</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, malloc.o(i.malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zeros
</UL>

<P><STRONG><a name="[109]"></a>matmul</STRONG> (Thumb, 1006 bytes, Stack size 120 bytes, nav_math.o(i.matmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = matmul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WheelObsCalAndNoiseSet
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
</UL>

<P><STRONG><a name="[f6]"></a>matrixSum</STRONG> (Thumb, 300 bytes, Stack size 48 bytes, nav_math.o(i.matrixSum))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = matrixSum &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Update_Phi_HP
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LeverarmTimeCorr2
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Earth_UP
</UL>

<P><STRONG><a name="[143]"></a>mcusendtopcdriversdata</STRONG> (Thumb, 290 bytes, Stack size 328 bytes, gdwatch.o(i.mcusendtopcdriversdata))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = mcusendtopcdriversdata &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_Output
</UL>

<P><STRONG><a name="[1ba]"></a>norm</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, nav_math.o(i.norm))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = norm &rArr; InnerDot &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InnerDot
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rv2q
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
</UL>

<P><STRONG><a name="[11e]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
</UL>

<P><STRONG><a name="[1fe]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[237]"></a>nvic_vector_table_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_vector_table_set))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[23b]"></a>parseFPGABuff</STRONG> (Thumb, 1538 bytes, Stack size 64 bytes, logger.o(i.parseFPGABuff))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = parseFPGABuff &rArr; __hardfp_exp &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[e0]"></a>parse_split_fnum</STRONG> (Thumb, 150 bytes, Stack size 40 bytes, nav_math.o(i.parse_split_fnum))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = parse_split_fnum &rArr; __hardfp_atof &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atof
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtok
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CliWriteConfig
</UL>

<P><STRONG><a name="[1f2]"></a>pmu_backup_write_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_pmu.o(i.pmu_backup_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[144]"></a>protocol_opticalgyro</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, ins_output.o(i.protocol_opticalgyro))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = protocol_opticalgyro &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_Output
</UL>

<P><STRONG><a name="[142]"></a>protocol_send</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, computerframeparse.o(i.protocol_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = protocol_send &rArr; frame_miscel_send &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_miscel_send
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NAV_Output
</UL>

<P><STRONG><a name="[138]"></a>qdelphi</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, nav_math.o(i.qdelphi))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = qdelphi &rArr; rv2q &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zeros
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rv2q
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnbmul
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KfFeedback
</UL>

<P><STRONG><a name="[10a]"></a>qnb2att</STRONG> (Thumb, 944 bytes, Stack size 184 bytes, nav_math.o(i.qnb2att))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KfFeedback
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
</UL>

<P><STRONG><a name="[107]"></a>qnbmul</STRONG> (Thumb, 802 bytes, Stack size 64 bytes, nav_math.o(i.qnbmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = qnbmul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qdelphi
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SINS_Update
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateQnb
</UL>

<P><STRONG><a name="[1f6]"></a>rcu_all_reset_flag_clear</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[24f]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[1f5]"></a>rcu_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[244]"></a>rcu_osci_on</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_osci_on))
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[23f]"></a>rcu_osci_stab_wait</STRONG> (Thumb, 342 bytes, Stack size 20 bytes, gd32f4xx_rcu.o(i.rcu_osci_stab_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[b3]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_configuration
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_PORT_INIT
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Master_Init
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Input
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
</UL>

<P><STRONG><a name="[206]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_deinit
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
</UL>

<P><STRONG><a name="[205]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_deinit
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
</UL>

<P><STRONG><a name="[245]"></a>rcu_rtc_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_rtc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[1f9]"></a>rcu_timer_clock_prescaler_config</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[1a5]"></a>rtc_flag_clear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[1a3]"></a>rtc_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[240]"></a>rtc_init</STRONG> (Thumb, 190 bytes, Stack size 20 bytes, gd32f4xx_rtc.o(i.rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_exit
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup
</UL>

<P><STRONG><a name="[241]"></a>rtc_init_mode_enter</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[242]"></a>rtc_init_mode_exit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_exit))
<BR><BR>[Called By]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[1f8]"></a>rtc_interrupt_enable</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[1f3]"></a>rtc_pre_config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, bsp_rtc.o(i.rtc_pre_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_pre_config &rArr; rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_rtc_clock_config
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[243]"></a>rtc_register_sync_wait</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_register_sync_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_register_sync_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[1f4]"></a>rtc_setup</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, bsp_rtc.o(i.rtc_setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = rtc_setup &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
</UL>

<P><STRONG><a name="[1a4]"></a>rtc_show_timestamp</STRONG> (Thumb, 388 bytes, Stack size 32 bytes, bsp_rtc.o(i.rtc_show_timestamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rtc_show_timestamp &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_timestamp_subsecond_get
<LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_timestamp_get
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[1f7]"></a>rtc_timestamp_enable</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_timestamp_enable))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[246]"></a>rtc_timestamp_get</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_timestamp_get))
<BR><BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>

<P><STRONG><a name="[247]"></a>rtc_timestamp_subsecond_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get))
<BR><BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>

<P><STRONG><a name="[21d]"></a>rtc_update</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, transplant.o(i.rtc_update))
<BR><BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_navi_and_gnss_send
</UL>

<P><STRONG><a name="[106]"></a>rv2q</STRONG> (Thumb, 348 bytes, Stack size 80 bytes, nav_math.o(i.rv2q))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = rv2q &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;norm
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qdelphi
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_IMU_Data
</UL>

<P><STRONG><a name="[216]"></a>sector_name_to_number</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, bsp_fmc.o(i.sector_name_to_number))
<BR><BR>[Called By]<UL><LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
</UL>

<P><STRONG><a name="[1a6]"></a>socket_buffer_config</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, tcpserver.o(i.socket_buffer_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = socket_buffer_config &rArr; CH395SetSocketSendBuf &rArr; xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetSocketSendBuf
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetSocketRecvBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TCPServer_Init
</UL>

<P><STRONG><a name="[22b]"></a>sow2Date</STRONG> (Thumb, 140 bytes, Stack size 104 bytes, time_unify.o(i.sow2Date))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = sow2Date &rArr; gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
</UL>

<P><STRONG><a name="[b8]"></a>spi_crc_off</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_crc_off))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_PORT_INIT
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
</UL>

<P><STRONG><a name="[b9]"></a>spi_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_PORT_INIT
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
</UL>

<P><STRONG><a name="[173]"></a>spi_i2s_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi395Exchange
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>

<P><STRONG><a name="[172]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi395Exchange
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>

<P><STRONG><a name="[171]"></a>spi_i2s_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi395Exchange
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>

<P><STRONG><a name="[b7]"></a>spi_init</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395_PORT_INIT
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
</UL>

<P><STRONG><a name="[1c6]"></a>sqrt</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[134]"></a>symmetry</STRONG> (Thumb, 150 bytes, Stack size 40 bytes, nav_math.o(i.symmetry))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = symmetry &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ODS_Angle_Estimation
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
</UL>

<P><STRONG><a name="[f1]"></a>synthesisLogBuf</STRONG> (Thumb, 644 bytes, Stack size 520 bytes, logger.o(i.synthesisLogBuf))
<BR><BR>[Stack]<UL><LI>Max Depth = 984<LI>Call Chain = synthesisLogBuf &rArr; time2str &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[1f0]"></a>syscfg_exti_line_config</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, gd32f4xx_syscfg.o(i.syscfg_exti_line_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = syscfg_exti_line_config
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
</UL>

<P><STRONG><a name="[20f]"></a>systick_clksource_set</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.systick_clksource_set))
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>

<P><STRONG><a name="[248]"></a>time2epoch</STRONG> (Thumb, 248 bytes, Stack size 56 bytes, time_unify.o(i.time2epoch))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = time2epoch &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
</UL>

<P><STRONG><a name="[249]"></a>time2str</STRONG> (Thumb, 244 bytes, Stack size 160 bytes, time_unify.o(i.time2str))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = time2str &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[1a9]"></a>timeSync</STRONG> (Thumb, 290 bytes, Stack size 128 bytes, bsp_rtc.o(i.timeSync))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = timeSync &rArr; gpst2utc &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;localtime
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
</UL>

<P><STRONG><a name="[233]"></a>timeadd</STRONG> (Thumb, 134 bytes, Stack size 48 bytes, time_unify.o(i.timeadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = timeadd &rArr; __hardfp_floor &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
</UL>

<P><STRONG><a name="[234]"></a>timediff</STRONG> (Thumb, 78 bytes, Stack size 40 bytes, time_unify.o(i.timediff))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = timediff &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
</UL>

<P><STRONG><a name="[203]"></a>timer_auto_reload_shadow_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[1ff]"></a>timer_channel_output_config</STRONG> (Thumb, 484 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_channel_output_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[201]"></a>timer_channel_output_mode_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[200]"></a>timer_channel_output_pulse_value_config</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[202]"></a>timer_channel_output_shadow_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_shadow_config))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[1fa]"></a>timer_deinit</STRONG> (Thumb, 374 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[1fd]"></a>timer_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[1fb]"></a>timer_init</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_init))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[1fc]"></a>timer_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[1aa]"></a>timer_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER2_IRQHandler
</UL>

<P><STRONG><a name="[1a7]"></a>timer_interrupt_flag_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER2_IRQHandler
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
</UL>

<P><STRONG><a name="[11a]"></a>trng_configuration</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, main.o(i.trng_configuration))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = trng_configuration &rArr; trng_ready_check
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_enable
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_ready_check
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[24b]"></a>trng_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_trng.o(i.trng_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = trng_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_configuration
</UL>

<P><STRONG><a name="[24c]"></a>trng_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_enable))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_configuration
</UL>

<P><STRONG><a name="[24e]"></a>trng_flag_get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_ready_check
</UL>

<P><STRONG><a name="[24d]"></a>trng_ready_check</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, main.o(i.trng_ready_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = trng_ready_check
</UL>
<BR>[Calls]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_configuration
</UL>

<P><STRONG><a name="[165]"></a>uart4sendmsg</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, gd32f4xx_it.o(i.uart4sendmsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_opticalgyro
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcusendtopcdriversdata
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaVector
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStop
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateStart
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateEnd
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaTime
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanR
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaKalmanQ
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGyroType
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGpsType
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnssInitValue
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaGnss
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFrequency
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFilter
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorGyro
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaFactorAcc
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDeviation
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDebugMode
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaDataOutType
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCoord
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaCalibration
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAngle
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaAll
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveParaToFlash
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RestoreFactory
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_4
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_3
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_2
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_1
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadPara_0
</UL>

<P><STRONG><a name="[23a]"></a>uart4sendmsg_canout</STRONG> (Thumb, 118 bytes, Stack size 264 bytes, bsp_can.o(i.uart4sendmsg_canout))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = uart4sendmsg_canout &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[238]"></a>uart6sendmsg</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, gd32f4xx_it.o(i.uart6sendmsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart6sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[221]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[1ac]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART6_IRQHandler
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
</UL>

<P><STRONG><a name="[1ad]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART6_IRQHandler
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
</UL>

<P><STRONG><a name="[220]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[224]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[22a]"></a>usart_flag_clear</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
</UL>

<P><STRONG><a name="[217]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[229]"></a>usart_hardware_flow_cts_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_hardware_flow_cts_config))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
</UL>

<P><STRONG><a name="[228]"></a>usart_hardware_flow_rts_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_hardware_flow_rts_config))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
</UL>

<P><STRONG><a name="[1ae]"></a>usart_interrupt_disable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART6_IRQHandler
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
</UL>

<P><STRONG><a name="[120]"></a>usart_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart6sendmsg
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateFirmsendmsg
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaUpdateSend
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetParaBaud
</UL>

<P><STRONG><a name="[1ab]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART6_IRQHandler
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
</UL>

<P><STRONG><a name="[227]"></a>usart_parity_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_parity_config))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
</UL>

<P><STRONG><a name="[222]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[226]"></a>usart_stop_bit_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_stop_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
</UL>

<P><STRONG><a name="[223]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[225]"></a>usart_word_length_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_word_length_set))
<BR><BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init6
</UL>

<P><STRONG><a name="[1b7]"></a>ushort2Buf</STRONG> (Thumb, 20 bytes, Stack size 12 bytes, firmwareupdatefile.o(i.ushort2Buf))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ushort2Buf
</UL>
<BR>[Calls]<UL><LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bufExchangeHL2pos
</UL>
<BR>[Called By]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_nav2up_SetHead
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartSend_nav2up_SetEnd
</UL>

<P><STRONG><a name="[239]"></a>wheel_is_running</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, computerframeparse.o(i.wheel_is_running))
<BR><BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[251]"></a>writeCSVFileHead</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, logger.o(i.writeCSVFileHead))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = writeCSVFileHead &rArr; CH378ByteWrite &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[f2]"></a>writeCSVLog</STRONG> (Thumb, 330 bytes, Stack size 40 bytes, logger.o(i.writeCSVLog))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = writeCSVLog &rArr; writeCSVFileHead &rArr; CH378ByteWrite &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetDiskStatus
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelaymS
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileOpen
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileCreate
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[af]"></a>xReadCH378Data</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.xReadCH378Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xReadCH378Data &rArr; SPI_Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetICVer
</UL>

<P><STRONG><a name="[c0]"></a>xReadCH395Data</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, ch395spi.o(i.xReadCH395Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xReadCH395Data &rArr; Spi395Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi395Exchange
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDCheckExist
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395GetCmdStatus
</UL>

<P><STRONG><a name="[a3]"></a>xWriteCH378Cmd</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.xWriteCH378Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetICVer
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
</UL>

<P><STRONG><a name="[a4]"></a>xWriteCH378Data</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.xWriteCH378Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xWriteCH378Data &rArr; SPI_Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
</UL>

<P><STRONG><a name="[be]"></a>xWriteCH395Cmd</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, ch395spi.o(i.xWriteCH395Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xWriteCH395Cmd &rArr; mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi395Exchange
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelayuS
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetTCPMss
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetStartPara
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetSocketSendBuf
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetSocketRecvBuf
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetMASKAddr
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetIPAddr
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetGWIPAddr
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDInitCH395
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDCheckExist
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395GetCmdStatus
</UL>

<P><STRONG><a name="[bf]"></a>xWriteCH395Data</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ch395spi.o(i.xWriteCH395Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xWriteCH395Data &rArr; Spi395Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi395Exchange
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetTCPMss
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetStartPara
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetSocketSendBuf
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395SetSocketRecvBuf
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetMASKAddr
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetIPAddr
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDSetGWIPAddr
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH395CMDCheckExist
</UL>

<P><STRONG><a name="[21f]"></a>xor_check</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, frame_analysis.o(i.xor_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = xor_check
</UL>
<BR>[Called By]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frame_pack_and_send
</UL>

<P><STRONG><a name="[23d]"></a>zeros</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, nav_math.o(i.zeros))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = zeros &rArr; malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qdelphi
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[1e7]"></a>conf_sensor</STRONG> (Thumb, 158 bytes, Stack size 32 bytes, bmp2.o(i.conf_sensor))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = conf_sensor &rArr; bmp2_soft_reset &rArr; bmp2_set_regs &rArr; interleave_data
</UL>
<BR>[Calls]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_soft_reset
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_set_regs
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_regs
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_os_mode
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_set_power_mode
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_set_config
</UL>

<P><STRONG><a name="[1e6]"></a>get_calib_param</STRONG> (Thumb, 236 bytes, Stack size 40 bytes, bmp2.o(i.get_calib_param))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = get_calib_param &rArr; bmp2_get_regs
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_regs
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_init
</UL>

<P><STRONG><a name="[1e9]"></a>interleave_data</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, bmp2.o(i.interleave_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = interleave_data
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_set_regs
</UL>

<P><STRONG><a name="[1e2]"></a>null_ptr_check</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, bmp2.o(i.null_ptr_check))
<BR><BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_set_regs
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_init
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_regs
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_compute_meas_time
</UL>

<P><STRONG><a name="[20e]"></a>set_os_mode</STRONG> (Thumb, 150 bytes, Stack size 0 bytes, bmp2.o(i.set_os_mode))
<BR><BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;conf_sensor
</UL>

<P><STRONG><a name="[24a]"></a>system_clock_200m_25m_hxtal</STRONG> (Thumb, 240 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_200m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[1a2]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_200m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[12b]"></a>Init_XPQ</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, nav_kf.o(i.Init_XPQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Init_XPQ
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_Init
</UL>

<P><STRONG><a name="[135]"></a>KfFeedback</STRONG> (Thumb, 468 bytes, Stack size 56 bytes, nav_kf.o(i.KfFeedback))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = KfFeedback &rArr; qnb2att &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qnb2att
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;qdelphi
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KF_UP2
</UL>

<P><STRONG><a name="[f9]"></a>LeverarmTimeCorr2</STRONG> (Thumb, 1756 bytes, Stack size 600 bytes, nav_kf.o(i.LeverarmTimeCorr2))
<BR><BR>[Stack]<UL><LI>Max Depth = 872<LI>Call Chain = LeverarmTimeCorr2 &rArr; __hardfp_tan &rArr; __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matrixSum
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;matmul
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;askew
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qnb2Cnb
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrHeading_PI
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPSObsCalAndNoiseSet
</UL>

<P><STRONG><a name="[1d6]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1bc]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[1d8]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1d7]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[6c]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL>
<P><STRONG><a name="[75]"></a>_fp_value</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[8f]"></a>_local_sscanf</STRONG> (Thumb, 54 bytes, Stack size 56 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
