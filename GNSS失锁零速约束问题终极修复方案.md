# GNSS失锁零速约束问题终极修复方案

## 问题现象
用户反馈GNSS失锁后出现严重问题：
1. **初始问题**: 东向速度0.9m/s，位置轨迹呈环形
2. **后续问题**: 晃动几下再静止后，北向速度达到1.94m/s并持续增长
3. **根本问题**: 零速约束无法有效抑制惯性导航的误差发散

## 终极修复策略

### 1. 分层速度阈值设计 (nav_const.h)
```c
// 进一步放宽GNSS失锁时的检测阈值
#define TH_acc_GNSS_LOST            (30*0.542*0.001)  // 放宽6倍
#define TH_gyr_GNSS_LOST            (30*0.013)        // 放宽6倍

// 分层速度阈值策略
#define SPEED_THRESHOLD_HIGH        2.0   // 高速阈值，超过此值绝不约束
#define SPEED_THRESHOLD_MID         1.0   // 中速阈值，需要严格条件
#define SPEED_THRESHOLD_LOW         0.3   // 低速阈值，相对宽松条件
```

### 2. 智能分层零速约束逻辑 (nav_imu.c)
```c
if(gnss_lost) {
    if(speed > SPEED_THRESHOLD_HIGH) {
        // 高速运动(>2.0m/s)，绝不约束
        ZUPT_flag = FAIL;
        hold_cnt = 0;
    }
    else if(speed < SPEED_THRESHOLD_LOW) {
        // 低速状态(<0.3m/s)，宽松条件约束
        if(acc_std < 2*th_acc_use && gyr_std < 2*th_gyr_use) {
            ZUPT_flag = SUCCESS;
            hold_cnt = ZUPT_HOLD_COUNT;
        }
        else if(hold_cnt > 0) {
            ZUPT_flag = SUCCESS;  // 持续期内保持约束
            hold_cnt--;
        }
        else {
            ZUPT_flag = FAIL;
        }
    }
    else if(speed < SPEED_THRESHOLD_MID) {
        // 中速状态(0.3-1.0m/s)，严格条件约束
        if(acc_std < th_acc_use && gyr_std < th_gyr_use) {
            ZUPT_flag = SUCCESS;
            hold_cnt = ZUPT_HOLD_COUNT;
        }
        else if(hold_cnt > 0) {
            ZUPT_flag = SUCCESS;
            hold_cnt--;
        }
        else {
            ZUPT_flag = FAIL;
        }
    }
    else {
        // 中高速状态(1.0-2.0m/s)，极其严格的条件
        if(acc_std < th_acc_use/4 && gyr_std < th_gyr_use/4 && speed < 1.5) {
            ZUPT_flag = SUCCESS;
            hold_cnt = ZUPT_HOLD_COUNT/8;  // 很短的持续时间
        }
        else {
            ZUPT_flag = FAIL;
            hold_cnt = 0;
        }
    }
}
```

### 3. 极大增强零速约束权重 (nav_kf.c)
```c
// GNSS失锁后极大增强零速约束权重
if(gnss_lost) {
    // 降低零速约束的观测噪声到原来的1/1000
    R_ZUPT[0] = ZUPTstd*ZUPTstd*0.001;  // 权重增强1000倍
    R_ZUPT[1] = ZUPTstd*ZUPTstd*0.001;
    R_ZUPT[2] = ZUPTstd*ZUPTstd*0.001;
}
```

## 修复原理

### 1. 分层策略的优势
- **低速区间(<0.3m/s)**: 使用宽松条件，确保真正静止时能够检测到
- **中速区间(0.3-1.0m/s)**: 使用严格条件，平衡检测率和误检率
- **中高速区间(1.0-2.0m/s)**: 使用极严格条件，避免运动时误约束
- **高速区间(>2.0m/s)**: 绝不约束，确保不会影响正常运动

### 2. 权重增强的作用
- **1000倍权重增强**: 使零速约束在卡尔曼滤波中占主导地位
- **强制收敛**: 即使有其他误差源，零速约束也能强制速度收敛到0
- **快速响应**: 一旦检测到零速，能够快速抑制速度发散

### 3. 持续机制的保障
- **持续计数器**: 确保零速约束不会因为短暂的IMU波动而中断
- **分层持续时间**: 不同速度区间使用不同的持续时间策略

## 针对用户问题的解决方案

### 问题1: 速度0.9m/s发散
- **解决**: 0.9m/s属于中速区间，使用严格条件检测
- **效果**: 在IMU相对稳定时能够检测到并应用零速约束

### 问题2: 速度1.94m/s持续增长
- **解决**: 1.94m/s属于中高速区间，使用极严格条件
- **效果**: 只有在IMU极其稳定时才约束，避免误约束
- **关键**: 1000倍权重增强确保一旦约束生效就能强制收敛

### 问题3: 晃动后静止速度不收敛
- **解决**: 静止后速度会快速降到低速区间(<0.3m/s)
- **效果**: 低速区间使用宽松条件，更容易检测到零速约束
- **保障**: 持续机制确保约束不会轻易中断

## 预期效果

### 立即效果
1. **速度发散得到控制**: 各个速度区间都有对应的约束策略
2. **静止检测更敏感**: 低速区间的宽松条件提高检测成功率
3. **约束效果更强**: 1000倍权重增强确保约束生效时能强制收敛

### 长期效果
1. **提高导航精度**: GNSS失锁后的导航误差显著减小
2. **增强系统鲁棒性**: 在各种运动状态下都能正确处理
3. **减少用户投诉**: 解决"画圈"和速度发散问题

## 部署建议
1. **分阶段测试**: 先测试静止场景，再测试动态场景
2. **参数微调**: 根据实际IMU性能可能需要微调阈值
3. **长期监控**: 观察不同使用场景下的表现
4. **备份机制**: 保留原始代码以便必要时回滚

这个终极修复方案通过分层策略、权重增强和持续机制的三重保障，应该能够彻底解决GNSS失锁后的速度发散问题。
