/***********************************************************************************
nav application module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|DengWei       |  2023-2-13         | Modify                     |
 Add EPSON_G370 sensor
|-----------+---------------+-------------------|  
***********************************************************************************/
#include "nav_includes.h"
#include <string.h>
#include <stdio.h>
#include "gnss.h"
#ifndef linux
#include "UartAdapter.h"
#endif
//int timecount = 0;
//ȫ�ֱ��� 
//NAV_Data_t NAV_Data;  

//Sensors_Data_t Sensors_Data = {0};
//Param_Data_t Param_Data = {0};
//Param_t Param_Data_temp = {0};
//CombineDataTypeDef *Sensors = &combineData; //����������ָ��
char g_ALGODebugInfo[1024]={0};
unsigned long int g_NavIndex=0;

#if 0
//���ݹ۲����˲�
double P_gyro_Cvn=(100.0)*(100.0);
double Q_gyro_Cvn =(0.001f)*(0.001f)*0.005;
double R_gyro_Cvn =(1.0f)*(1.0f);

//�Ӽƹ۲����˲�
double P_acc_Cvn=(20.0)*(20.0);
double Q_acc_Cvn =(0.001f)*(0.001f)*0.005;
double R_acc_Cvn =(1.0f)*(1.0f);
#endif

///////////////////////////////////////////////////////////////////////////////////

//�����ṹ���������
_NAV_Data_Full_t NAV_Data_Full = {0};	 //����������
_NAV_Data_Out_t NAV_Data_Out = {0};      //�������
_NAV_Funsion_Status_Time_t g_NAV_Funsion_Status_Time={0};//�ں�״̬������ʱ��




//��������Ƿ�Χ��(-180 180]
double CorrHeading(double orginheading)
{
	if(orginheading<=-180)
	{
		return orginheading+360;
	}
	else if(orginheading>180)
	{
		return orginheading-360;
	}
	else
	{
		return orginheading;
	}
}
//****Guolong Zhang*****[-pi~pi]*****
double CorrHeading_PI(double orginheading)//*****����Ϊrad***
{
	if(orginheading<=-PI)
	{
		return orginheading+2*PI;
	}
	else if(orginheading>PI)
	{
		return orginheading-2*PI;
	}
	else
	{
		return orginheading;
	}
}



/******************************************************************************
*ԭ  �ͣ�void KinematicEstimation(_NAV_Data_Full_t* NAV_Data_Full_p)
*��  �ܣ��˶�ѧģ�͹���
*��  �룺��
*��  ������
*******************************************************************************/
void KinematicEstimation(_NAV_Data_Full_t* NAV_Data_Full_p)
{
#if 0
	//��������״̬
	//����1:��ֹ
	//��ֹ����:�ϸ�ʱ���ٶȽӽ�0�� ����ϵ��û�м��ٶȼƽ��ٶ�
	if(		NAV_Data_Full_p->SINS.vn[0]<MAX_STATIC_VN 
		&& 	NAV_Data_Full_p->SINS.vn[1]<MAX_STATIC_VN 
		&&   )
#endif		
	
}

/******************************************************************************
*ԭ  �ͣ�void Param_Data_Init(_NAV_Data_Full_t* NAV_Data_Full_p)
*��  �ܣ�������ʼ��
*��  �룺��
*��  ������
*******************************************************************************/
void Param_Data_Init(_NAV_Data_Full_t* NAV_Data_Full_p)
{
#ifndef linux
	combineData.Param.methodType =	E_METHOD_TYPE_KALMAN;//0:ȫ��kalmnan , 1:ֱ�����ٺ�λ����
	combineData.Param.sim = E_SIM_MODE_NORMAL;//0:����ģʽΪ��������*****1:����ģʽΪ����ģʽ****
#endif

	NAV_Data_Full_p->KF.use_gps_flag 		=	E_FUNSION_GPS;	
	NAV_Data_Full_p->Gnss_Use_Status		=	E_NAV_SUPPORT_RTK_ALL_STATUS;//E_NAV_SUPPORT_RTK_FIEX_STATUS;//E_NAV_SUPPORT_RTK_ALL_STATUS;//E_NAV_SUPPORT_RTK_FIEX_STATUS
		
	NAV_Data_Full_p->ODS.ods_flag 			=	E_ODS_WHEEL_FLAG_NONE;//;E_ODS_WHEEL_FLAG_NONE;E_ODS_WHEEL_FLAG_HAVE
	NAV_Data_Full_p->ODS.ods_caculat_flag	=	RETURN_FAIL;//******

//	if(E_IMU_MANU_460 == NAV_Data_Full_p->memsType)
//	{
//		NAV_Data_Full_p->KF.measure_flag_head	=	E_KALMAN_MEASURE_HEADING_YES;//E_KALMAN_MEASURE_HEADING_NO
//	}
//	else if(E_IMU_MANU_ADIS16465 == NAV_Data_Full_p->memsType)
//	{
//		NAV_Data_Full_p->KF.measure_flag_head =	E_KALMAN_MEASURE_HEADING_YES;
//	}
//	else
//	{
//		NAV_Data_Full_p->KF.measure_flag_head	=	E_KALMAN_MEASURE_HEADING_NO;
//	}
//	
	NAV_Data_Full_p->KF.measure_flag_head = E_KALMAN_MEASURE_HEADING_NO;
	NAV_Data_Full_p->KF.measure_flag_Wheel = RETURN_FAIL;

	// 零速约束标志位设置 - 根据ZUPT_flag决定
	if(NAV_Data_Full_p->ZUPT_flag == RETURN_SUCESS)
	{
		NAV_Data_Full_p->KF.measure_flag_ZUPT = RETURN_SUCESS;
	}
	else
	{
		NAV_Data_Full_p->KF.measure_flag_ZUPT = RETURN_FAIL;
	}

	NAV_Data_Full_p->KF.measure_flag_NHC = RETURN_FAIL;
	NAV_Data_Full_p->KF.measure_flag_vn = E_KALMAN_MEASURE_VEL_NO;
	NAV_Data_Full_p->KF.measure_flag_pos = E_KALMAN_MEASURE_POS_NO;
	NAV_Data_Full_p->KF.measure_flag = E_KALMAN_MEASURE_UPDATE_NONE;
}

void NavDRMode(unsigned int testcount)
{

	if(	   (NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID || NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID_RTK) //'A'
		&& NAV_Data_Full.GPS.rtkStatus == E_GPS_RTK_FIXED
		&& E_SIM_MODE_NORMAL == combineData.Param.sim
	   )
	{
		
//		SINS_UP(&NAV_Data_Full);
//		KF_UP2(&NAV_Data_Full,&combineData);
		SINS_Update(&NAV_Data_Full);
		KF_UP2(&NAV_Data_Full,&combineData);
		
	}
	else if(		(NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID || NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID_RTK) //'A'
				&& NAV_Data_Full.GPS.rtkStatus == E_GPS_RTK_FIXED
				&& E_SIM_MODE_DEBUG == combineData.Param.sim
				&& testcount<combineData.Param.lostepoch
	   		)
	{
		//SINS_UP(&NAV_Data_Full);
		SINS_Update(&NAV_Data_Full);
		KF_UP2(&NAV_Data_Full,&combineData);
	}
	else
	{
//		SetNavFunsionSource(E_FUNSION_WHEEL);
//		SINS_UP_DR(&NAV_Data_Full);
//		//ֱ��ʹ�����ٽ����ٶȸ���
//		TEST_UP(&NAV_Data_Full);
		SINS_Update(&NAV_Data_Full);
		KF_UP2(&NAV_Data_Full,&combineData);
	}
}

void NavKalmanMode(unsigned int testcount)
{
	#if 0
	if(0 == combineData.Param.HP)
	{
		SINS_UP(&NAV_Data_Full);
	}
	else
	{
		//SINS_UP_HP(&NAV_Data_Full);
		SINS_UP(&NAV_Data_Full);
	}
	#endif
	SINS_Update(&NAV_Data_Full);

	// GNSS失锁后的直接速度约束机制
	// 这是针对Z轴加速度计误差导致北向速度发散的直接修复
	int gnss_lost = (NAV_Data_Full.GPS.gps_up_flag == E_GPS_NO_UPDATE ||
	                 NAV_Data_Full.GPS.Position_Status == E_GPS_POS_INVALID);

	if(gnss_lost && NAV_Data_Full.ZUPT_flag == RETURN_SUCESS)
	{
		// 在零速约束生效时，直接强制约束速度
		// 这可以有效抑制由于Z轴加速度计误差导致的北向速度发散
		double speed_decay_factor = 0.95; // 速度衰减因子

		// 对所有速度分量进行强制衰减
		NAV_Data_Full.SINS.vn[0] *= speed_decay_factor; // 东向速度
		NAV_Data_Full.SINS.vn[1] *= speed_decay_factor; // 北向速度
		NAV_Data_Full.SINS.vn[2] *= speed_decay_factor; // 天向速度

		// 如果速度已经很小，直接置零
		if(fabs(NAV_Data_Full.SINS.vn[0]) < 0.01) NAV_Data_Full.SINS.vn[0] = 0.0;
		if(fabs(NAV_Data_Full.SINS.vn[1]) < 0.01) NAV_Data_Full.SINS.vn[1] = 0.0;
		if(fabs(NAV_Data_Full.SINS.vn[2]) < 0.01) NAV_Data_Full.SINS.vn[2] = 0.0;
	}

//	if(E_SIM_MODE_DEBUG == combineData.Param.sim)
//	{
//		if(testcount>=combineData.Param.lostepoch)
//		{
//			//NAV_Data_Full.KF.use_gps_flag = E_FUNSION_WHEEL;//E_FUNSION_WHEEL;//E_FUNSION_MOTION;
//			//hGPSData.rtkStatus = 0 ;
//			//NAV_Data_Full.GPS.gps_up_flag = E_GPS_NO_UPDATE;
//		}
//	}
	KF_UP2(&NAV_Data_Full,&combineData);
}

//��������
/******************************************************************************
*ԭ  �ͣ�void NAV_function(void)
*��  �ܣ��������������������ڳ�������+ODS+GPS
*��  �룺��
*��  ������
*******************************************************************************/
void NAV_function(void)//�������㺯��
{
	static unsigned long row=0;
	row++;
	if(row%10000 ==0)
	printf("�����ܳ����ݵ�%ld��\n",row);
	//static long int navindex=0;
	static unsigned long testcount=0;
	g_NavIndex++;
	//��ȡ���������� �� ϵͳ����******
	Get_Param_Data(&NAV_Data_Full,&combineData);//��λ�����ݼ���
	Get_IMU_Data(&NAV_Data_Full,&combineData);
	Get_GNSS_Data(&NAV_Data_Full,&combineData);
	Get_ODS_Data(&NAV_Data_Full,&combineData);

	//�˶�ѧ����Kinematic estimation
	//KinematicEstimation(&NAV_Data_Full);
	//*******����GPSʧ��****��ʼ��10min��ʼִ��**VS����***
	#if 0
	int i = 0;
	if(NAV_Data_Full.Nav_Status > E_NAV_STATUS_SINS_KF_INITIAL)
	for (i = 0; i < outnum; i++)
	{
		if ((NAV_Data_Full.GPS.gpssecond982 / 1000.0 - NAV_Data_Full.outage_start_per[i] > 0.0)
			&& (NAV_Data_Full.GPS.gpssecond982 / 1000.0 - NAV_Data_Full.outage_start_per[i] < outagetime))
		{
			NAV_Data_Full.GPS.gps_up_flag = E_GPS_NO_UPDATE;
		}
	}
	#endif
	//************����GPSʧ��debug����*********keilʵʱ����*************
	if((NAV_Data_Full.Nav_Status == E_NAV_STATUS_IN_NAV)
		  &&(E_SIM_MODE_DEBUG == combineData.Param.sim)
	  )
	  {
		   NAV_Data_Full.GPS.gps_up_flag = E_GPS_NO_UPDATE;
				 NAV_Data_Full.GPS.rtkStatus = E_GPS_RTK_INVALID;
				 hGPSData.rtkStatus = E_GPS_RTK_INVALID ;//*******������λ����ʾ0****
				 NAV_Data_Full.GPS.Position_Status=E_GPS_POS_INVALID;
	  }
	
	//testcount++;
	
	switch(NAV_Data_Full.Nav_Status)
	{
		case E_NAV_STATUS_START://��ʼ���� ���ص�������*****0
		{
			//�������ò���
			Param_Data_Init(&NAV_Data_Full);
			//���ر궨����
			Load_Standard_Data(&NAV_Data_Full,&combineData);//����flash����
			SetNavStatus(E_NAV_STATUS_ROUTH_ALIGN);
		 //NAV_Data_Full.Nav_Status = E_NAV_STATUS_ROUTH_ALIGN;  
		}
		break;
		case E_NAV_STATUS_ROUTH_ALIGN://SINS��ʼ��׼*****1
		{
			if(		//(NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID || NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID_RTK) //'A'��Ч//V
					   (NAV_Data_Full.GPS.Position_Status != E_GPS_POS_INVALID) 
			      &&(NAV_Data_Full.GPS.gps_up_flag == E_GPS_IS_UPDATE)
			      &&(NAV_Data_Full.GPS.headingStatus		== E_GPS_RTK_FIXED)		
			  )						
			{	
						NAV_Data_Full.GPSlastnum++;			
						if(NAV_Data_Full.GPSlastnum>25)//******5Hz**�ݶ�5s***
						{
							SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
						}
				 //SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
				//NAV_Data_Full.Nav_Status = E_NAV_STATUS_SINS_KF_INITIAL;// NAV_INS_STATUS_ROUTH_ALIGN;
			}			
		}
		break;
		case E_NAV_STATUS_SINS_KF_INITIAL://SINS KF��ʼ��*****2
		{
 			SINS_Init(&NAV_Data_Full);			
			if(NAV_Data_Full.SINS.Init_flag == 1)
			{
				if(NAV_Data_Full.Nav_Standard_flag ==E_NAV_STANDARD_PROCCSSED)
				{
					SetNavStatus(E_NAV_STATUS_IN_NAV);
					//g_KF_UP2_gps_delay_cunt = 0;//��ʼ�л������ڵ�����Ҫ����Ϊ0
					//NAV_Data_Full.Nav_Status = E_NAV_STATUS_IN_NAV;
				}
				else
				{
					SetNavStatus(E_NAV_STATUS_SYSTEM_STANDARD);
					//g_KF_UP2_gps_delay_cunt = 0;//��ʼ�л���ϵͳ�궨��Ҫ����Ϊ0
					//NAV_Data_Full.Nav_Status = E_NAV_STATUS_SYSTEM_STANDARD;
				}
				KF_Init(&NAV_Data_Full); 
			}
		}
		break;
		case E_NAV_STATUS_SYSTEM_STANDARD://ϵͳ�궨*****3
		{		
			/*
			if(0 == combineData.Param.HP)
			{
				SINS_UP(&NAV_Data_Full);
			}
			else
			{
				//SINS_UP_HP(&NAV_Data_Full);
				SINS_UP(&NAV_Data_Full);
			}
			*/
			SINS_Update(&NAV_Data_Full);
			KF_UP2(&NAV_Data_Full,&combineData);
			
//			if (sqrt(NAV_Data_Full.GPS.ve*NAV_Data_Full.GPS.ve + NAV_Data_Full.GPS.vu*NAV_Data_Full.GPS.vu + NAV_Data_Full.GPS.vn*NAV_Data_Full.GPS.vn)>2
//				&&	NAV_Data_Full.GPS.rtkStatus 		== E_GPS_RTK_FIXED)
//			{
//				timecount ++ ;
//			}
//			else if (timecount >=1)
//			{
//				timecount ++;
//			}
#if 0
			if(g_NavIndex==100000)
			{
				SetNavStatus(E_NAV_STATUS_IN_NAV);
				//KF_Init(&NAV_Data_Full);
				//g_KF_UP2_gps_delay_cunt = 0;//��ʼ�л������ڵ�����Ҫ����Ϊ0
				//NAV_Data_Full.Nav_Status = E_NAV_STATUS_IN_NAV;
			}
#endif			
			if(NAV_Data_Full.Nav_Standard_flag ==E_NAV_STANDARD_PROCCSSED)
			{
				SetNavStatus(E_NAV_STATUS_IN_NAV);
				//KF_Init(&NAV_Data_Full);
				//g_KF_UP2_gps_delay_cunt = 0;//��ʼ�л������ڵ�����Ҫ����Ϊ0
				//NAV_Data_Full.Nav_Status = E_NAV_STATUS_IN_NAV;
				//����궨����
				//Save_Standard_Data(&NAV_Data_Full,&combineData);
#ifdef linux
				inav_log(INAVMD(LOG_DEBUG),"gnssAtt_from_vehicle2=%f",NAV_Data_Full.Param.gnssAtt_from_vehicle2[2]);
#endif				
			}
			
		}
		break;
		case E_NAV_STATUS_IN_NAV://�������� *****4
		{	
			++testcount;		
//			SINS_Update(&NAV_Data_Full);
//		  KF_UP2(&NAV_Data_Full,&combineData);
			//NavKalmanMode(g_NavIndex);
			if(E_METHOD_TYPE_KALMAN == combineData.Param.methodType)
			{
				NavKalmanMode(g_NavIndex);//
			}
			/*
			else if(E_METHOD_TYPE_DR == combineData.Param.methodType)
			{
				NavDRMode(g_NavIndex);
			}
			*/
			else
			{
   #ifdef linux
				inav_log(INAVMD(LOG_ERR),"methodType=%d",combineData.Param.methodType);
   #endif		
			}
		}
		break;
		case E_NAV_STATUS_STOP://ֹͣ����*****5
		{
 			//StopNavigation();
		}
		break;
		default: //�����쳣״̬*****6
		{
			break;	
		}
	}
#ifdef linux	
	//��ӡ��ǰ��Ԫ��Ŀ��״̬Ǩ��Nav_Status,Nav_Standard_flag
	PrintOutStateChange(g_NavIndex,&NAV_Data_Full);
	//NMEA out
#endif
	
	Out_Data_Up(&NAV_Data_Out); //������ݸ���

#if 0	
	if(20000==g_NavIndex)
	{
		SendOBSIMUData(&NAV_Data_Out,&NAV_Data_Full);
		SendOBSGNSSData(&NAV_Data_Out,&NAV_Data_Full);
		SendSINSData(&NAV_Data_Out,&NAV_Data_Full);
	}
#endif

}

//��������
/******************************************************************************
*ԭ  �ͣ�void NAV_function_LD_TEST(void)
*��  �ܣ�����������������
*��  �룺��
*��  ������
*******************************************************************************/
void NAV_function_LD_TEST(void)//�������㺯��
{
	//��ȡ���������ݺ�ϵͳ����
	Get_Param_Data(&NAV_Data_Full,&combineData);
	Get_IMU_Data(&NAV_Data_Full,&combineData);
	Get_GNSS_Data(&NAV_Data_Full,&combineData);
	NAV_Data_Full.GPS.Heading = 0;
	Get_ODS_Data(&NAV_Data_Full,&combineData);
	
	switch(NAV_Data_Full.Nav_Status)
	{
		case E_NAV_STATUS_START://��ʼ���� ���ص�������
		{
			//�������ò���
			Param_Data_Init(&NAV_Data_Full);
			//���ر궨����,�궨�����ڹ۲����ж�ȡ�����������
			Load_Standard_Data(&NAV_Data_Full,&combineData);
			SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
		}
		break;	
		case E_NAV_STATUS_SINS_KF_INITIAL://SINS KF��ʼ��
		{
			NAV_Data_Full.SINS.pos[0]	=	22.74418555666*DEG2RAD;//pNAV_GNSS_RESULT->latitude*DEG2RAD;//
			NAV_Data_Full.SINS.pos[1]	=	113.8147628866*DEG2RAD;//pNAV_GNSS_RESULT->longitude * DEG2RAD;//
			NAV_Data_Full.SINS.pos[2]	=	0;//pNAV_GNSS_RESULT->altitude ;//�̵߳�λm 
			NAV_Data_Full.SINS.vn[0] = 0;
			NAV_Data_Full.SINS.vn[1] = 0;
			NAV_Data_Full.SINS.vn[2] = 0;
 			SINS_Init(&NAV_Data_Full);			
			SetNavStatus(E_NAV_STATUS_SYSTEM_STANDARD);
			
		}
		break;
		case E_NAV_STATUS_SYSTEM_STANDARD://ϵͳ�궨
		{
				Earth_UP(&NAV_Data_Full,NAV_Data_Full.SINS.pos,NAV_Data_Full.SINS.vn);
				MahonyUpdate_NoMAG(&NAV_Data_Full);	
		}
		break;
	}
#ifdef linux	
	//��ӡ��ǰ��Ԫ��Ŀ��״̬Ǩ��Nav_Status,Nav_Standard_flag
	//PrintOutStateChange(g_NavIndex,&NAV_Data_Full);
	//NMEA out
#endif
	Out_Data_Up(&NAV_Data_Out); //������ݸ���

}


//��������
/******************************************************************************
*ԭ  �ͣ�void NAV_function_UAV(void)
*��  �ܣ��������������������ھ������˻�
*��  �룺��
*��  ������
*******************************************************************************/
void NAV_function_UAV(void)//�������㺯��
{
	g_NavIndex++;
	//��ȡ���������� �� ϵͳ����
	Get_Param_Data(&NAV_Data_Full,&combineData);
	Get_IMU_Data(&NAV_Data_Full,&combineData);
	Get_GNSS_Data(&NAV_Data_Full,&combineData);
	
	switch(NAV_Data_Full.Nav_Status)
	{
		case E_NAV_STATUS_START://��ʼ���� ���ص�������
		{
			//�������ò���
			Param_Data_Init(&NAV_Data_Full);
			//���ر궨����,�궨�����ڹ۲����ж�ȡ�����������
			Load_Standard_Data(&NAV_Data_Full,&combineData);
			SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
		    //NAV_Data_Full.Nav_Status = E_NAV_STATUS_ROUTH_ALIGN;  
		}
		break;
		case E_NAV_STATUS_SINS_KF_INITIAL://SINS KF��ʼ��
		{
 			SINS_Init(&NAV_Data_Full);			
			if(NAV_Data_Full.SINS.Init_flag == 1)
			{
				SetNavStatus(E_NAV_STATUS_IN_NAV);
			}
		}
		break;
		case E_NAV_STATUS_IN_NAV://�������� 
		{
			MahonyUpdate(&NAV_Data_Full);	
		}
		break;
		case E_NAV_STATUS_STOP://ֹͣ����
		{
 			//StopNavigation();
		}
		break;
		default: //�����쳣״̬
		{
			break;	
		}
	}
#ifdef linux	
	//��ӡ��ǰ��Ԫ��Ŀ��״̬Ǩ��Nav_Status,Nav_Standard_flag
	PrintOutStateChange(g_NavIndex,&NAV_Data_Full);
	//NMEA out
#endif
	//���ASCIIЭ������
	Out_Data_Up(&NAV_Data_Out); //������ݸ���
}




////////////////////////end/////////////////////////////////////////////////////
