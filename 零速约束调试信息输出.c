// 在nav_imu.c的零速约束检测部分添加调试输出
// 这可以帮助诊断为什么速度<0.1m/s时零速约束没有生效

// 在零速约束检测的关键位置添加以下调试代码：

#ifdef DEBUG_ZUPT
static int debug_count = 0;
debug_count++;

// 每100次输出一次调试信息
if(debug_count % 100 == 0) {
    printf("=== 零速约束调试信息 ===\n");
    printf("Nav_Status: %d\n", NAV_Data_Full_p->Nav_Status);
    printf("GNSS失锁: %s\n", gnss_lost ? "是" : "否");
    printf("当前速度: %.4f m/s\n", speed);
    printf("东向速度: %.4f m/s\n", NAV_Data_Full_p->SINS.vn[0]);
    printf("北向速度: %.4f m/s\n", NAV_Data_Full_p->SINS.vn[1]);
    printf("天向速度: %.4f m/s\n", NAV_Data_Full_p->SINS.vn[2]);
    printf("加速度标准差: %.6f\n", sqrt(acc_std2));
    printf("陀螺标准差: %.6f\n", sqrt(gyr_std2));
    printf("加速度阈值: %.6f\n", th_acc_use);
    printf("陀螺阈值: %.6f\n", th_gyr_use);
    printf("ZUPT_flag: %d\n", NAV_Data_Full_p->ZUPT_flag);
    printf("ZUPT_hold_cnt: %d\n", NAV_Data_Full_p->ZUPT_hold_cnt);
    printf("========================\n");
}
#endif

// 使用方法：
// 1. 在编译时定义DEBUG_ZUPT宏
// 2. 或者直接将printf语句添加到代码中
// 3. 观察输出，找出零速约束没有生效的原因

// 可能的问题排查：
// 1. Nav_Status是否在正确的状态（应该>=2）
// 2. gnss_lost是否正确检测到GNSS失锁
// 3. 速度计算是否正确
// 4. IMU标准差是否过大，无法满足检测条件
// 5. ZUPT_flag和ZUPT_hold_cnt的状态变化

// 临时调试版本（可以直接添加到代码中）：
/*
在nav_imu.c的零速约束检测部分添加：

static int debug_output_count = 0;
debug_output_count++;

if(debug_output_count % 200 == 0) { // 每2秒输出一次（200Hz/200=1Hz）
    // 这里可以添加printf或者其他输出方式
    // 根据您的系统选择合适的输出方法
}

// 特别关注的情况：
if(speed < 0.15 && gnss_lost) {
    // 速度很小且GNSS失锁，应该强制约束
    // 如果这里ZUPT_flag不是RETURN_SUCESS，说明有问题
}
*/

// 建议的修复策略：
// 1. 首先确认Nav_Status的值
// 2. 确认GNSS失锁检测是否正确
// 3. 如果以上都正确，考虑进一步降低速度阈值
// 4. 或者完全移除IMU条件检查，只要速度小就强制约束

// 最激进的修复方案（如果调试发现其他条件都满足）：
/*
if(gnss_lost && speed < 0.15) {
    // 不管任何其他条件，直接强制约束
    NAV_Data_Full_p->ZUPT_flag = RETURN_SUCESS;
    NAV_Data_Full_p->ZUPT_hold_cnt = ZUPT_HOLD_COUNT;
}
*/
