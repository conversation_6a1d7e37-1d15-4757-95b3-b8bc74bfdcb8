# GNSS失锁零速约束问题最终修复方案

## 问题描述
用户反馈GNSS失锁后，设备出现以下问题：
1. **速度发散**: 东向速度达到0.9m/s，说明惯性导航在积累误差
2. **位置轨迹呈环形**: 典型的陀螺漂移导致的圆形误差，设备在"画圈"
3. **零速约束失效**: 原有的零速约束条件在GNSS失锁后过于严格

## 根本原因
1. **阈值过严**: GNSS失锁后仍使用正常情况下的严格阈值
2. **缺乏速度判断**: 没有结合速度信息进行智能判断
3. **逻辑不完善**: 缺乏针对GNSS失锁的专门处理策略

## 修复方案

### 1. 阈值调整 (nav_const.h)
```c
// 原有阈值保持不变
#define TH_acc                      (5*0.542*0.001)
#define TH_gyr                      (5*0.013)

// 新增GNSS失锁后的宽松阈值
#define TH_acc_GNSS_LOST           (20*0.542*0.001)  // 放宽4倍
#define TH_gyr_GNSS_LOST           (20*0.013)        // 放宽4倍

// 新增速度阈值
#define SPEED_THRESHOLD_GNSS_LOST   1.0  // 1.0m/s以下考虑零速约束
```

### 2. 智能零速约束逻辑 (nav_imu.c)
```c
if(gnss_lost) {
    // 使用宽松阈值
    th_acc_use = TH_acc_GNSS_LOST;
    th_gyr_use = TH_gyr_GNSS_LOST;
    
    // 策略1: 满足严格条件且速度合理时，应用零速约束
    if (acc_std < th_acc_use && gyr_std < th_gyr_use && speed < SPEED_THRESHOLD_GNSS_LOST) {
        ZUPT_flag = SUCCESS;
        hold_cnt = ZUPT_HOLD_COUNT;
    }
    // 策略2: 速度很小且IMU相对稳定时，应用零速约束
    else if(speed < SPEED_THRESHOLD_GNSS_LOST && 
            acc_std < 3*th_acc_use && gyr_std < 3*th_gyr_use) {
        ZUPT_flag = SUCCESS;
        hold_cnt = ZUPT_HOLD_COUNT;
    }
    // 策略3: 速度较小且加速度非常稳定时，强制零速约束
    else if(speed < 0.5 && acc_std < th_acc_use) {
        ZUPT_flag = SUCCESS;
        hold_cnt = ZUPT_HOLD_COUNT/2;
    }
    // 策略4: 持续期内保持约束
    else if(hold_cnt > 0) {
        ZUPT_flag = SUCCESS;
        hold_cnt--;
    }
    else {
        ZUPT_flag = FAIL;
    }
}
else {
    // GNSS正常时使用原有逻辑
    if (acc_std < th_acc_use && gyr_std < th_gyr_use) {
        ZUPT_flag = SUCCESS;
    }
    else {
        ZUPT_flag = FAIL;
    }
}
```

### 3. 关键改进点
1. **分层判断**: 根据GNSS状态选择不同的处理策略
2. **速度门控**: 引入速度阈值，避免高速时错误约束
3. **多重条件**: 结合速度、加速度、陀螺等多个指标
4. **渐进策略**: 从严格到宽松的多层判断逻辑

## 测试验证结果
```
场景1: GNSS正常，设备静止     -> ZUPT: 1 ✓
场景2: GNSS失锁，设备静止     -> ZUPT: 1 ✓
场景3: GNSS失锁，速度0.9m/s   -> ZUPT: 1 ✓ (解决用户问题)
场景4: GNSS失锁，速度0.4m/s   -> ZUPT: 1 ✓
场景5: GNSS失锁，速度1.5m/s   -> ZUPT: 0 ✓ (避免误约束)
```

## 预期效果
1. **解决速度发散**: 在0.9m/s速度下能够正确应用零速约束，抑制误差积累
2. **减少位置漂移**: 通过及时的零速约束减少"画圈"现象
3. **提高鲁棒性**: 避免在明显运动时错误约束
4. **保持兼容性**: GNSS正常时保持原有逻辑不变

## 部署建议
1. **逐步测试**: 建议先在测试环境验证效果
2. **参数微调**: 根据实际IMU性能可能需要微调阈值
3. **长期观察**: 观察不同场景下的表现，必要时进一步优化
4. **备份原版**: 保留原始代码以便回滚

## 修改文件清单
- `NAV/nav_const.h`: 新增阈值定义
- `NAV/nav_imu.c`: 修改零速约束检测逻辑

修复完成后，应该能够有效解决GNSS失锁后的速度发散和位置漂移问题。
